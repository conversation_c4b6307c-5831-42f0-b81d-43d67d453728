package com.stt.android.device.watchface

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.device.watchface.navigation.WatchFaceLibraryRoute
import com.stt.android.device.watchface.navigation.watchFaceLibraryDestination

class MyWatchFaceLibraryActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentWithM3Theme {
            val navController = rememberNavController()
            NavHost(
                navController = navController,
                startDestination = WatchFaceLibraryRoute.WATCH_FACE_LIBRARY
            ) {
                watchFaceLibraryDestination(
                    onNavigateUp = {}
                )
            }
        }
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context): Intent {
            return Intent(context, MyWatchFaceLibraryActivity::class.java)
        }
    }
}
