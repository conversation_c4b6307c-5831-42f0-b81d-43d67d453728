package com.stt.android.device.watchface.library

import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.device.watchface.MyWatchFaceViewModel
import com.suunto.connectivity.watchface.WatchFace

@Composable
fun MyWatchFaceLibraryScreen(
    viewModel: MyWatchFaceViewModel,
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {

}

@Composable
internal fun MyWatchFaceLibraryContent(
    installedInWatch: List<WatchFace>,
    watchFacesInLibrary: List<WatchFaceEntity>,
    modifier: Modifier = Modifier,
    watchMaxLimitCount: Int = 10,
) {
    LazyColumn(
        modifier = modifier.narrowContent()
    ) {
        watchFacesInWatch(
            watchFaceCountInWatch = installedInWatch.size,
            watchMaxLimitCount = watchMaxLimitCount,
        )

        watchFacesInLibrary(
            watchFaces = watchFacesInLibrary
        )

        findNewWatchFaces()
    }
}
