package com.stt.android.device.watchface.library

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.LocalContentColor
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.coil.placeholder
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import com.stt.android.suuntoplus.ui.SuuntoPlusIconSizeDefaults
import com.stt.android.suuntoplus.ui.SuuntoPlusItemDetailsScreenLabels
import com.stt.android.suuntoplus.ui.WatchStatus
import com.stt.android.suuntoplus.ui.WatchStatusBadge
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.Locale
import com.stt.android.R as BaseR

internal fun LazyListScope.watchFacesInLibrary(
    watchFaces: List<WatchFaceEntity>
) {
    items(watchFaces) { watchFace ->
        WatchFaceLibraryItem(
            title = watchFace.name,
            shortDescription = watchFace.shortDescription ?: "",
            watchPreviewImageUrl = watchFace.iconUrl ?: "",
            dimmed = false,
            watchStatus = WatchStatus.INSTALLED,
            expirationDate = null,
            onClick = {},
            enableButton = true,
            buttonText = "Install",
            onButtonClick = {},
            itemLabels = watchFace.labels?.map { SuuntoPlusItemLabel(it) }?.toImmutableList(),
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun WatchFaceLibraryItem(
    title: String,
    shortDescription: String,
    watchPreviewImageUrl: String,
    dimmed: Boolean,
    watchStatus: WatchStatus?,
    onButtonClick: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enableButton: Boolean = true,
    buttonText: String? = null,
    buttonTextColor: Color? = null,
    expirationDate: String? = null,
    itemLabels: ImmutableList<SuuntoPlusItemLabel>? = null,
) {
    Surface(
        modifier = modifier,
        onClick = onClick,
    ) {
        val contentColor =
            if (dimmed) MaterialTheme.colorScheme.mediumGrey else MaterialTheme.colorScheme.nearBlack

        CompositionLocalProvider(LocalContentColor provides contentColor) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
                modifier = Modifier
                    .padding(
                        top = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.medium,
                    ),
            ) {
                val boxSize =
                    SuuntoPlusIconSizeDefaults.featureListIconSize + SuuntoPlusIconSizeDefaults.featureListWatchStatusBadgeOffset * 2.0f

                Box(
                    modifier = Modifier
                        .size(boxSize)
                        .alpha(if (dimmed) 0.6f else 1.0f)
                ) {
                    Card(
                        shape = CircleShape,
                        elevation = SuuntoPlusIconSizeDefaults.guideListIconElevation,
                        modifier = Modifier
                            .size(SuuntoPlusIconSizeDefaults.featureListIconSize)
                            .align(Alignment.Center)
                    ) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(watchPreviewImageUrl)
                                .crossfade(true)
                                .apply {
                                    if (LocalInspectionMode.current) {
                                        placeholder(LocalContext.current, R.drawable.widg_hr)
                                    }
                                }
                                .build(),
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize()
                        )
                    }

                    watchStatus?.let {
                        WatchStatusBadge(
                            status = it,
                            modifier = Modifier.align(Alignment.TopEnd)
                        )
                    }
                }

                Column(Modifier.weight(1f)) {
                    if (expirationDate != null) {
                        Text(
                            text = stringResource(
                                BaseR.string.suunto_plus_feature_expired_date,
                                expirationDate
                            ),
                            maxLines = 1,
                            modifier = Modifier.padding(bottom = MaterialTheme.spacing.xsmall)
                        )
                    } else if (watchStatus == WatchStatus.INCOMPATIBLE) {
                        Text(
                            text = stringResource(BaseR.string.suunto_plus_feature_not_compatible_with_watch),
                            color = MaterialTheme.colorScheme.darkGrey,
                            maxLines = 1,
                            modifier = Modifier.padding(bottom = MaterialTheme.spacing.xsmall)
                        )
                    }

                    Text(
                        text = title,
                        maxLines = 2,
                        style = MaterialTheme.typography.bodyLargeBold,
                    )

                    if (shortDescription.isNotBlank()) {
                        Text(
                            text = shortDescription,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall)
                        )
                    }

                    itemLabels?.let {
                        SuuntoPlusItemDetailsScreenLabels(
                            labels = itemLabels,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall)
                        )
                    }
                }

                if (buttonText != null) {
                    val textColor = buttonTextColor ?: MaterialTheme.colorScheme.primary
                    TextButton(
                        onClick = onButtonClick,
                        modifier = Modifier.padding(end = MaterialTheme.spacing.small),
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = textColor,
                            disabledContentColor = textColor.copy(alpha = 0.6f)
                        ),
                        enabled = enableButton
                    ) {
                        Text(buttonText.uppercase(Locale.getDefault()))
                    }
                } else {
                    Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
                }
            }
        }
    }
}

@Preview
@Composable
private fun WatchFaceItemPreview() {
    M3AppTheme {
        Surface {
            WatchFaceLibraryItem(
                title = "Cooper Test",
                shortDescription = "Run 12 min at max speed",
                watchPreviewImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzcoop01.png",
                dimmed = false,
                watchStatus = WatchStatus.INSTALLED,
                expirationDate = null,
                onClick = {},
                enableButton = true,
                buttonText = "Uninstall",
                buttonTextColor = MaterialTheme.colorScheme.error,
                onButtonClick = {},
            )
        }
    }
}

@Preview
@Composable
private fun DimmedWatchFaceItemPreview() {
    M3AppTheme {
        Surface {
            WatchFaceLibraryItem(
                title = "Cooper Test",
                shortDescription = "Run 12 min at max speed",
                watchPreviewImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzcoop01.png",
                dimmed = true,
                watchStatus = WatchStatus.INSTALLED,
                expirationDate = null,
                onClick = {},
                enableButton = true,
                buttonText = "Install",
                onButtonClick = {},
            )
        }
    }
}

@Preview
@Composable
private fun ExpiredWatchFaceItemPreview() {
    M3AppTheme {
        Surface {
            val expirationTime = Instant.ofEpochMilli(1640995200000L).atZone(ZoneId.systemDefault())
            val expirationDate = LocalDate.from(expirationTime)
                .format(DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT))

            WatchFaceLibraryItem(
                title = "World Run",
                shortDescription = "World Run",
                watchPreviewImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzwifl01.png",
                dimmed = true,
                watchStatus = null,
                expirationDate = expirationDate,
                onClick = {},
                enableButton = false,
                buttonText = null,
                onButtonClick = {},
            )
        }
    }
}

@Preview
@Composable
private fun IncompatibleWatchFaceItemPreview() {
    M3AppTheme {
        Surface {
            WatchFaceLibraryItem(
                title = "Weather",
                shortDescription = "Insights",
                watchPreviewImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzweth01.png",
                dimmed = false,
                watchStatus = WatchStatus.INCOMPATIBLE,
                expirationDate = null,
                onClick = {},
                enableButton = false,
                buttonText = null,
                onButtonClick = {},
            )
        }
    }
}
