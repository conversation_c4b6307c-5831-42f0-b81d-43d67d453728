package com.stt.android.offlinemaps

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.stt.android.maps.SuuntoChinaOfflineRegion
import com.stt.android.maps.SuuntoMarker
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.ui.LinkAnnotation
import com.stt.android.offlinemaps.ui.OfflineRegionPreviewScreen
import com.stt.android.offlinemaps.ui.OfflineRegionsScreen
import kotlinx.collections.immutable.toImmutableList

internal object OfflineMapsSelectionNavigation {
    const val OFFLINE_REGION_GROUPS_ROUTE: String = "OFFLINE_REGION_GROUPS"
    const val OFFLINE_REGIONS_ROUTE: String = "OFFLINE_REGIONS"
    const val OFFLINE_REGION_PREVIEW_ROUTE: String = "OFFLINE_REGION_PREVIEW"
}

internal fun NavGraphBuilder.buildNavigationGraph(
    navController: NavController,
    onClose: (userStartedRegionDownload: Boolean) -> Unit,
    openLibrary: (regionId: String?) -> Unit,
    openWifiSetup: (toAddNetwork: Boolean) -> Unit,
    openOsmDisclaimer: () -> Unit,
    onDownloadInfoLink: (LinkAnnotation) -> Unit,
    openDownloadInfo: Boolean,
    viewModel: OfflineMapsSelectionViewModel,
) {
    composable(
        route = OfflineMapsSelectionNavigation.OFFLINE_REGION_GROUPS_ROUTE,
    ) {
        OfflineRegionsScreen(
            listData = viewModel.offlineRegionCatalogue,
            catalogue = viewModel.offlineRegionCatalogue,
            loading = viewModel.loading,
            watchConnected = viewModel.isWatchConnected,
            wifiEnabled = viewModel.isWifiEnabled,
            cancellingDownload = viewModel.cancellingDownload,
            requestingDownload = viewModel.requestingDownload,
            downloadError = viewModel.downloadError,
            savedWifiNetworksCount = viewModel.savedWifiNetworksCount,
            updateAvailableCount = viewModel.updateAvailableCount,
            freeSpaceAvailable = viewModel.freeSpaceAvailable,
            wifiDisabledDismissed = viewModel.wifiDisabledDismissed,
            wifiSetupDismissed = viewModel.wifiSetupDismissed,
            batteryTipDismissed = viewModel.batteryTipDismissed,
            onOfflineRegionGroupSelected = {
                viewModel.selectedOfflineRegionGroup = it
                navController.popBackStack(
                    route = OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE,
                    inclusive = true,
                )
                navController.navigate(OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE)
            },
            onOfflineRegionSelected = {
                viewModel.selectedOfflineRegion = it
                viewModel.getAllChinaRegions(it)
                navController.popBackStack(
                    route = OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE,
                    inclusive = true,
                )
                navController.navigate(OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE)
            },
            onCancel = { viewModel.cancelDownload(it.id) },
            onRetry = { viewModel.downloadOfflineRegion(it.id) },
            onViewInLibrary = { openLibrary(it?.id) },
            onEnableWifi = { openWifiSetup(false) },
            onWifiSetup = { openWifiSetup(true) },
            onDownloadInfoLink = onDownloadInfoLink,
            onDownloadFailedDismiss = { viewModel.downloadError = null },
            onWifiDisabledDismiss = { viewModel.onWifiDisabledDismissed() },
            onWifiSetupDismiss = { viewModel.onWifiSetupDismissed() },
            onBatteryTipDismiss = { viewModel.batteryTipDismissed = true },
            onEnableSearchMode = { viewModel.trackSearchScreen() },
            navigateUp = { onClose(viewModel.userStartedRegionDownload) },
            openDownloadInfo = openDownloadInfo
        )
    }

    composable(OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE) {
        OfflineRegionsScreen(
            listData = OfflineRegionListData.Group(
                viewModel.selectedOfflineRegionGroup
                    ?: OfflineRegionResult.OfflineRegionGroup.EMPTY // Should never end up here
            ),
            catalogue = viewModel.offlineRegionCatalogue,
            loading = viewModel.loading,
            watchConnected = viewModel.isWatchConnected,
            wifiEnabled = viewModel.isWifiEnabled,
            cancellingDownload = viewModel.cancellingDownload,
            requestingDownload = viewModel.requestingDownload,
            downloadError = viewModel.downloadError,
            savedWifiNetworksCount = viewModel.savedWifiNetworksCount,
            updateAvailableCount = viewModel.updateAvailableCount,
            freeSpaceAvailable = viewModel.freeSpaceAvailable,
            wifiDisabledDismissed = viewModel.wifiDisabledDismissed,
            wifiSetupDismissed = viewModel.wifiSetupDismissed,
            batteryTipDismissed = viewModel.batteryTipDismissed,
            onOfflineRegionGroupSelected = {
                viewModel.selectedOfflineRegionGroup = it
                navController.popBackStack(
                    route = OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE,
                    inclusive = true
                )
                navController.navigate(OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE)
            },
            onOfflineRegionSelected = {
                viewModel.selectedOfflineRegion = it
                viewModel.getAllChinaRegions(it)
                navController.popBackStack(
                    route = OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE,
                    inclusive = true
                )
                navController.navigate(OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE)
            },
            onCancel = { viewModel.cancelDownload(it.id) },
            onRetry = { viewModel.downloadOfflineRegion(it.id) },
            onViewInLibrary = { openLibrary(it?.id) },
            onEnableWifi = { openWifiSetup(false) },
            onWifiSetup = { openWifiSetup(true) },
            onDownloadInfoLink = onDownloadInfoLink,
            onDownloadFailedDismiss = { viewModel.downloadError = null },
            onWifiDisabledDismiss = { viewModel.onWifiDisabledDismissed() },
            onWifiSetupDismiss = { viewModel.onWifiSetupDismissed() },
            onBatteryTipDismiss = { viewModel.batteryTipDismissed = true },
            onEnableSearchMode = { viewModel.trackSearchScreen() },
            navigateUp = navController::navigateUp
        )
    }

    composable(OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE) {
        OfflineRegionPreviewScreen(
            region = viewModel.selectedOfflineRegion
                ?: OfflineRegionResult.OfflineRegion.EMPTY, // Should never end up here
            mapStyles = viewModel.mapStyles,
            onDownloadRegion = {
                viewModel.downloadOfflineRegion(
                    regionId = it.id,
                )
                navController.navigateUp()
            },
            onOsmDisclaimer = openOsmDisclaimer,
            onDeleteRegion = {
                // Only supported through offline maps library
            },
            navigateUp = {
                navController.navigateUp()
            },
            onSelectRegion = { id ->
                viewModel.selectChinaRegion(navController.context, id)
            },
            allChinaRegionsAndSelectState = viewModel.allChinaRegionsAndSelectState.toImmutableList(),
            selectedChinaRegions = viewModel.selectedChinaDownloadRegions.toImmutableList(),
            onLoadAllChinaOfflineRegionSuccess = { suuntoChinaOfflineRegions: List<SuuntoChinaOfflineRegion>, suuntoMarkers: List<SuuntoMarker> ->
                viewModel.chinaOfflineRegionOverlays = suuntoChinaOfflineRegions
                viewModel.chinaRegionDownloadStateMarkers = suuntoMarkers
            },
            watchStorageFull = viewModel.watchStorageFullState.value,
            onWatchStorageFullDialogDismissRequest = {
                viewModel.watchStorageFullState.value = false
            },
            onMapScaleListener = {
                viewModel.scaleMarkers(it)
            }
        )
    }
}
