package com.stt.android.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionMapStyle
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import kotlinx.collections.immutable.ImmutableList

interface OfflineRegionDataSource {

    suspend fun getCatalogue(
        deviceSerial: String,
        latLng: LatLng?,
        capabilities: String?,
        includeNearbyGroups: Boolean,
    ): OfflineRegionListData.Catalogue

    suspend fun putDownloadOrder(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?,
    ): OfflineRegionResult.OfflineRegion

    suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult>

    suspend fun getLibrary(
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion>

    suspend fun getCatalogueRegion(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?,
    ): OfflineRegionResult.OfflineRegion

    suspend fun getLibraryRegion(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion

    suspend fun deleteRegion(
        deviceSerial: String,
        region: OfflineRegionResult.OfflineRegion,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion

    suspend fun getMapStyles(): List<OfflineRegionMapStyle>

    suspend fun resetLibrary(
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion>
}
