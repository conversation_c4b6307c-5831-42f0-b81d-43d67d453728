package com.stt.android.offlinemaps

import android.content.Context
import android.content.SharedPreferences
import androidx.annotation.VisibleForTesting
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.core.content.ContextCompat
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.maps.SuuntoChinaOfflineRegion
import com.stt.android.maps.SuuntoMarker
import com.stt.android.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.offlinemaps.common.OfflineRegionDownloadOperators
import com.stt.android.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.offlinemaps.domain.SetWatchKeyUseCase
import com.stt.android.offlinemaps.entity.FreeSpaceAvailable
import com.stt.android.offlinemaps.entity.OfflineMapRegionAndSelectState
import com.stt.android.offlinemaps.entity.OfflineRegionDownloadError
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionMapStyle
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.usecases.location.LastKnownLocationUseCase
import com.stt.android.utils.STTConstants
import com.stt.android.watch.wifi.domain.GetSavedWifiNetworksCountUseCase
import com.stt.android.watch.wifi.domain.GetWifiEnabledUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
class OfflineMapsSelectionViewModel @Inject constructor(
    private val offlineRegionRepository: OfflineRegionRepository,
    private val lastKnownLocationUseCase: LastKnownLocationUseCase,
    private val savedWifiNetworksCountUseCase: GetSavedWifiNetworksCountUseCase,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val getWifiEnabledUseCase: GetWifiEnabledUseCase,
    private val setWatchKeyUseCase: SetWatchKeyUseCase,
    private val downloadOperatorsDelegate: OfflineRegionDownloadOperators,
    @SuuntoSharedPrefs sharedPreferences: SharedPreferences,
    private val offlineMapsAnalytics: OfflineMapsAnalytics,
    private val dispatchers: CoroutinesDispatchers
) : ViewModel(), OfflineRegionDownloadOperators by downloadOperatorsDelegate {

    private var loadJob: Job? = null

    private var updateJob: Job? = null

    private var networkCountJob: Job? = null

    private var totalSpace: Long? = null

    var loading by mutableStateOf(false)
        private set

    var cancellingDownload by mutableStateOf(false)
        private set

    var requestingDownload by mutableStateOf(false)
        private set

    var userStartedRegionDownload = false
        private set

    // By default, assume we have wifi setup. We do not want to ask the user to setup wifi if
    // e.g. watch is connected, but getting network count takes time
    var savedWifiNetworksCount by mutableIntStateOf(1)
        private set

    var wifiDisabledDismissed by mutableStateOf(true) // set initially true until value received from watch

    var wifiSetupDismissed by mutableStateOf(true) // set initially true until watch returns count of saved networks

    var batteryTipDismissed by mutableStateOf(false)

    var downloadError: OfflineRegionDownloadError? by mutableStateOf(null)

    var isWatchConnected by mutableStateOf(true)

    var isWifiEnabled: Boolean by mutableStateOf(true) // set initially true until value received from watch

    var offlineRegionCatalogue by mutableStateOf(OfflineRegionListData.Catalogue())
        private set

    var mapStyles: ImmutableList<OfflineRegionMapStyle> by mutableStateOf(persistentListOf())
        private set

    var selectedOfflineRegionGroup by mutableStateOf<OfflineRegionResult.OfflineRegionGroup?>(null)

    var selectedOfflineRegion by mutableStateOf<OfflineRegionResult.OfflineRegion?>(null)

    var updateAvailableCount by mutableIntStateOf(0)

    var freeSpaceAvailable: FreeSpaceAvailable? by mutableStateOf(null)

    var allChinaRegionsAndSelectState = mutableStateListOf<OfflineMapRegionAndSelectState>()

    var chinaOfflineRegionOverlays = emptyList<SuuntoChinaOfflineRegion>()

    var chinaRegionDownloadStateMarkers = emptyList<SuuntoMarker>()

    // only for download china region
    val selectedChinaDownloadRegions = mutableStateListOf<OfflineRegionResult.OfflineRegion>()

    // need check the watch storage space, when select china region
    var watchStorageFullState = mutableStateOf(false)

    init {
        setTotalStorageSpace()
        loadOfflineRegionGroups()
        observeIsWatchConnected()
        observeWifiEnabled()
        pollDownloadOrderStatuses()
        sharedPreferences.edit {
            putBoolean(
                STTConstants.SuuntoPreferences.KEY_HAS_OPENED_OFFLINE_MAPS,
                true
            )
        }
        observeStorageSpaceAndUpdateCount()
    }

    private fun setChinaOfflineRegionOverlaySelectState(regionId: String, color: Int) {
        val findOverLay =
            chinaOfflineRegionOverlays.find { it.id == regionId }
        findOverLay?.let { overlay ->
            overlay.mask?.setLayerColor(color)
        }
    }

    /**
     * when zoom out map and the scale < MARKER_SCALE_THRESHOLD_VALUE, and scale the markers to make them smaller
     */
    fun scaleMarkers(scale: Float) {
        if (scale < MARKER_SCALE_THRESHOLD_VALUE) {
            chinaRegionDownloadStateMarkers.forEach {
                it.setIconScale(scale / SCALE_VALUE)
            }
        } else {
            chinaRegionDownloadStateMarkers.forEach {
                it.setIconScale(1f)
            }
        }
    }

    private fun updateWatchStorageSpace(addRegion: OfflineRegionResult.OfflineRegion) {
        var selectedChinaRegionSize = selectedChinaDownloadRegions.sumOf { it.size ?: 0 }
        selectedChinaRegionSize += addRegion.size ?: 0
        freeSpaceAvailable?.let {
            watchStorageFullState.value =
                it.freeSpace - (selectedChinaRegionSize + (selectedOfflineRegion?.size ?: 0)) < 0
        }
    }

    fun getAllChinaRegions(region: OfflineRegionResult.OfflineRegion) {
        allChinaRegionsAndSelectState.clear()
        selectedChinaDownloadRegions.clear()
        val group = offlineRegionCatalogue.groups.find { it.name == region.groupName }
        group?.apply {
            if (batchDownloadAllowed == true) {
                allChinaRegionsAndSelectState.addAll(
                    regions.map {
                        OfflineMapRegionAndSelectState(it, !it.downloadAvailable)
                    }.filter { it.region.id != region.id }
                )
            }
        }
    }

    fun selectChinaRegion(context:Context, regionId: String) {
        val index = allChinaRegionsAndSelectState.indexOfFirst { it.region.id == regionId }
        if (index >= 0) {
            val offlineMapRegionAndSelectState = allChinaRegionsAndSelectState[index]
            updateWatchStorageSpace(offlineMapRegionAndSelectState.region)
            if (!watchStorageFullState.value) {
                allChinaRegionsAndSelectState[index] =
                    offlineMapRegionAndSelectState.copy(
                        selectState = !offlineMapRegionAndSelectState.selectState
                    )
                val currentSelectRegion = allChinaRegionsAndSelectState[index]
                val selectState = currentSelectRegion.selectState
                if (selectState) {
                    selectedChinaDownloadRegions.add(currentSelectRegion.region)
                } else {
                    selectedChinaDownloadRegions.remove(currentSelectRegion.region)
                }
                setChinaOfflineRegionOverlaySelectState(
                    regionId,
                    if (selectState) {
                        ContextCompat.getColor(context, R.color.suunto_blue)
                    } else {
                        ContextCompat.getColor(context, R.color.near_white)
                    }
                )
            }
        }
    }

    fun downloadOfflineRegion(regionId: String) {
        val selectedChinaDownloadRegionIds = selectedChinaDownloadRegions.map { region ->
            region.id
        }
        val downloadIds = listOf(regionId) + selectedChinaDownloadRegionIds
        requestingDownload = true
        viewModelScope.launch {
            updateJob?.join() // wait for possible update job to complete

            userStartedRegionDownload = true
            wifiDisabledDismissed = false
            wifiSetupDismissed = false

            downloadIds.forEach { id ->
                val region = getRegion(id) ?: return@forEach
                runSuspendCatching {
                    if (region.downloadAvailable) {
                        actOnRegion(region) { target: OfflineRegionResult.OfflineRegion ->
                            downloadOfflineRegion(target.id, region.groupName)
                        }
                    }
                    offlineMapsAnalytics.trackDownloadStarted(
                        regionName = region.name,
                        ongoingRegionDownloads = offlineRegionCatalogue.groups.flatMap { it.regions }
                            .count { it.downloading || it.downloadRequested },
                    )
                }.onFailure { e ->
                    downloadError = OfflineRegionDownloadError(
                        regionName = region.name,
                        description = if (e is ClientError.Conflict) {
                            e.description // Localized description from the backend.
                        } else {
                            null
                        }
                    )
                    Timber.w(e, "Failed to download region id:$id: ids($downloadIds)")

                    if (e is ClientError.Conflict) {
                        trackDownloadBlocked(AnalyticsPropertyValue.DownloadMapsIssueType.WATCH_STORAGE_FULL)
                    }
                }
            }
            requestingDownload = false
        }
    }

    fun cancelDownload(regionId: String) {
        cancellingDownload = true
        viewModelScope.launch {
            runSuspendCatching {
                updateJob?.join() // wait for possible update job to complete
                val region = getRegion(regionId) ?: return@runSuspendCatching
                if (region.cancellable) {
                    actOnRegion(region) { target: OfflineRegionResult.OfflineRegion ->
                        deleteDownload(target)
                    }
                }

                offlineMapsAnalytics.trackDownloadCancelled(
                    regionName = region.name,
                    ongoingRegionDownloads = offlineRegionCatalogue.groups.flatMap { it.regions }
                        .count { it.downloading || it.downloadRequested },
                )
            }.onSuccess {
                cancellingDownload = false
            }.onFailure { e ->
                Timber.w(e, "Failed to cancel download: id($regionId)")
                cancellingDownload = false
            }
        }
    }

    private suspend fun actOnRegion(
        region: OfflineRegionResult.OfflineRegion,
        action: suspend (OfflineRegionResult.OfflineRegion) -> OfflineRegionResult.OfflineRegion
    ) {
        val group = offlineRegionCatalogue.groups.first { it.regions.contains(region) }
        val updated = action(region)
        val updatedGroup = replaceRegion(group, region, updated)
        val updatedNearby = replaceNearby(offlineRegionCatalogue.nearby, region, updated)
        selectedOfflineRegion = updated
        selectedOfflineRegionGroup = updatedGroup
        val updatedGroups = replaceGroup(group, updatedGroup)
        offlineRegionCatalogue = OfflineRegionListData.Catalogue(
            nearby = updatedNearby,
            groups = updatedGroups
        )
    }

    private fun replaceRegion(
        group: OfflineRegionResult.OfflineRegionGroup,
        region: OfflineRegionResult.OfflineRegion,
        updated: OfflineRegionResult.OfflineRegion
    ): OfflineRegionResult.OfflineRegionGroup {
        val updatedGroup = group.copy(
            regions = group.regions.map {
                when (it.id) {
                    region.id -> {
                        updated.copy(adjacentRegions = it.adjacentRegions)
                    }

                    else -> it
                }
            }.toPersistentList()
        )
        return updatedGroup
    }

    private fun replaceNearby(
        nearby: ImmutableList<OfflineRegionResult>,
        region: OfflineRegionResult.OfflineRegion,
        updated: OfflineRegionResult.OfflineRegion
    ): PersistentList<OfflineRegionResult> = nearby.map {
        if (it is OfflineRegionResult.OfflineRegion && it.id == region.id) {
            updated
        } else {
            it
        }
    }.toPersistentList()

    private fun replaceGroup(
        group: OfflineRegionResult.OfflineRegionGroup,
        updatedGroup: OfflineRegionResult.OfflineRegionGroup
    ): PersistentList<OfflineRegionResult.OfflineRegionGroup> = offlineRegionCatalogue.groups.map {
        when (it.id) {
            group.id -> updatedGroup
            else -> it
        }
    }.toPersistentList()

    @VisibleForTesting
    fun loadOfflineRegionGroups() {
        loadJob = viewModelScope.launch {
            loading = true
            runSuspendCatching {
                val hourAgo = System.currentTimeMillis() - 1.hours.inWholeMilliseconds
                val latLng = lastKnownLocationUseCase.getLastKnownLocation(
                    skipPassiveProvider = false,
                    timeInMilliSecondsSinceEpoch = hourAgo
                )
                coroutineScope {
                    val catalogueDeferred =
                        async { offlineRegionRepository.getOfflineRegionCatalogue(latLng) }
                    val mapStylesDeferred = async { offlineRegionRepository.getMapStyles() }
                    val setWatchKeyDeferred =
                        async { runSuspendCatching { setWatchKeyUseCase.setToken() } }
                    offlineRegionCatalogue = catalogueDeferred.await()
                    mapStyles = mapStylesDeferred.await()
                    setWatchKeyDeferred.await() // Executed to make sure the watch has the token
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to get offline region groups")
            }

            loading = false
        }
    }

    private fun pollDownloadOrderStatuses() {
        viewModelScope.launch(dispatchers.main) {
            loadJob?.join()

            repeat(1000) {
                delay(15.seconds)
                if (!cancellingDownload && !requestingDownload) {
                    supervisorScope {
                        updateJob = launch(dispatchers.main) {
                            updateDownloadOrderStatuses()
                        }
                        updateJob?.join()
                    }
                }
            }
        }
    }

    private fun observeWifiEnabled() {
        viewModelScope.launch {
            val watchConnectedFlow = isWatchConnectedUseCase.isWatchConnected()
                .catch { Timber.w(it, "Unable to get watch connection state.") }
                .distinctUntilChanged()

            watchConnectedFlow
                .flatMapLatest {
                    if (it) {
                        wifiDisabledDismissed = false
                        getWifiEnabledUseCase.run()
                    } else {
                        flow {}
                    }
                }
                .distinctUntilChanged()
                .catch {
                    Timber.w(it, "Failed to observe wifi enabled state.")
                }
                .collectLatest {
                    isWifiEnabled = it
                }
        }
    }

    @VisibleForTesting
    suspend fun updateDownloadOrderStatuses() = runSuspendCatching {
        Timber.d("Updating download order statuses...")
        val library = offlineRegionRepository.getLibrary()
        val catalogue = offlineRegionCatalogue
        val nearby = catalogue.nearby
        val groups = catalogue.groups
        val updatedCatalogue = withContext(dispatchers.computation) {
            val downloadOrders = library.associate { it.id to it.downloadOrder }

            val updatedNearby = nearby.map {
                if (it is OfflineRegionResult.OfflineRegion) {
                    val order = downloadOrders[it.id]
                    it.copy(downloadOrder = order)
                } else {
                    it
                }
            }.toPersistentList()

            val updatedGroups = groups.map { group ->
                val regions = group.regions.map { region ->
                    val order = downloadOrders[region.id]
                    region.copy(downloadOrder = order)
                }
                group.copy(regions = regions.toPersistentList())
            }.toPersistentList()

            OfflineRegionListData.Catalogue(
                nearby = updatedNearby,
                groups = updatedGroups
            )
        }

        selectedOfflineRegion = selectedOfflineRegion?.id?.let { regionId ->
            updatedCatalogue.groups.flatMap { it.regions }.firstOrNull { it.id == regionId }
        }
        selectedOfflineRegionGroup = selectedOfflineRegionGroup?.id?.let { groupId ->
            updatedCatalogue.groups.firstOrNull { it.id == groupId }
        }

        offlineRegionCatalogue = updatedCatalogue
        Timber.d("DONE: Updating download order statuses...")
    }.onFailure { Timber.w(it, "Failed to update download order statuses") }

    private fun getRegion(regionId: String): OfflineRegionResult.OfflineRegion? =
        offlineRegionCatalogue.groups.flatMap { it.regions }.firstOrNull { it.id == regionId }

    private fun observeIsWatchConnected() {
        viewModelScope.launch {
            isWatchConnectedUseCase.isWatchConnected()
                .catch { Timber.w(it, "Unable to get watch connection state.") }
                .distinctUntilChanged()
                .collectLatest {
                    isWatchConnected = it
                    if (isWatchConnected) {
                        observeSavedNetworksCount()
                    } else {
                        networkCountJob?.cancel()
                    }
                }
        }
    }

    private fun observeSavedNetworksCount() {
        networkCountJob = viewModelScope.launch {
            savedWifiNetworksCountUseCase.run()
                .catch { Timber.w(it, "Unable to get saved networks count.") }
                .distinctUntilChanged()
                .collectLatest {
                    savedWifiNetworksCount = it
                }
        }
    }

    fun onWifiDisabledDismissed() {
        wifiDisabledDismissed = true
        trackDownloadBlocked(AnalyticsPropertyValue.DownloadMapsIssueType.WIFI_OFF)
    }

    fun onWifiSetupDismissed() {
        wifiSetupDismissed = true
        trackDownloadBlocked(AnalyticsPropertyValue.DownloadMapsIssueType.WIFI_NOT_SETUP)
    }

    private fun trackDownloadBlocked(issueType: String) {
        offlineMapsAnalytics.trackDownloadBlocked(
            issueType
        )
    }

    fun trackSearchScreen() {
        offlineMapsAnalytics.trackSearchScreen()
    }

    private fun observeStorageSpaceAndUpdateCount() {
        snapshotFlow { offlineRegionCatalogue }
            .onEach {
                updateAvailableCount = withContext(dispatchers.computation) {
                    it.groups
                        .flatMap { it.regions }
                        .count { it.updateAvailable }
                }
                freeSpaceAvailable = withContext(dispatchers.computation) {
                    totalSpace?.let { total ->
                        val used = it.groups
                            .flatMap { it.regions }
                            .filter { it.downloadRequested || it.downloading || it.downloaded }
                            .sumOf { it.size ?: 0L }
                        val free = total - used
                        FreeSpaceAvailable(
                            freeSpace = total - used,
                            freeSpacePercentage = (100f * free / total).roundToInt()
                        )
                    }
                }
            }
            .catch { Timber.w(it, "Observing storage space and update count failed.") }
            .launchIn(viewModelScope)
    }

    private fun setTotalStorageSpace() = viewModelScope.launch(dispatchers.computation) {
        runSuspendCatching {
            totalSpace = getMapStorageSize()
        }.onFailure { e ->
            Timber.w(e, "Determining total storage space failed")
        }
    }
    companion object {
        const val MARKER_SCALE_THRESHOLD_VALUE = 4.0f
        const val SCALE_VALUE = 5.0f
    }
}
