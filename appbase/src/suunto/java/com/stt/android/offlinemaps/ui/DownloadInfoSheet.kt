package com.stt.android.offlinemaps.ui

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.BottomSheetHandle

@Composable
fun DownloadInfoSheet(
    onDownloadInfoLink: (LinkAnnotation) -> Unit,
    modifier: Modifier = Modifier
) {
    BottomSheetHandle()
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .padding(
                start = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.small,
                end = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.xlarge
            ),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(id = R.string.dl_info_title),
            style = MaterialTheme.typography.bodyXLargeBold,
        )
        DownloadInfoSheetLinkItem(
            imageRes = R.drawable.dl_info_wifi,
            textRes = R.string.dl_info_wifi_format_string,
            linkRes = R.string.dl_info_wifi_format_param,
            linkAnnotation = LinkAnnotation.WIFI_SETUP,
            onDownloadInfoLink = onDownloadInfoLink,
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.small,
                bottom = MaterialTheme.spacing.medium
            )
        )
        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_download,
            textRes = R.string.dl_info_download,
            modifier = Modifier.padding(
                bottom = MaterialTheme.spacing.medium
            )
        )
        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_plug_charger,
            textRes = R.string.dl_info_plug_charger,
            modifier = Modifier.padding(
                bottom = MaterialTheme.spacing.medium
            )
        )
        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_charger,
            textRes = R.string.dl_info_charger,
            modifier = Modifier.padding(
                bottom = MaterialTheme.spacing.medium
            )
        )
        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_download_time,
            textRes = R.string.dl_info_download_time,
            modifier = Modifier.padding(
                bottom = MaterialTheme.spacing.medium
            )
        )
        DownloadInfoSheetLinkItem(
            imageRes = R.drawable.dl_info_library,
            textRes = R.string.dl_info_library_format_string,
            linkRes = R.string.dl_info_library_format_param,
            linkAnnotation = LinkAnnotation.LIBRARY,
            onDownloadInfoLink = onDownloadInfoLink,
            modifier = Modifier.padding(
                bottom = MaterialTheme.spacing.medium
            )
        )
    }
}

@Composable
private fun DownloadInfoSheetItem(
    @DrawableRes imageRes: Int,
    @StringRes textRes: Int,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Image(
            painter = painterResource(id = imageRes),
            contentDescription = null,
        )
        Text(
            text = stringResource(id = textRes),
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

@Composable
private fun DownloadInfoSheetLinkItem(
    @DrawableRes imageRes: Int,
    @StringRes textRes: Int,
    @StringRes linkRes: Int,
    linkAnnotation: LinkAnnotation,
    onDownloadInfoLink: (LinkAnnotation) -> Unit,
    modifier: Modifier = Modifier,
) {
    val annotatedText = buildAnnotatedString {
        val linkText = stringResource(id = linkRes)
        val text = stringResource(id = textRes, linkText)
        val offset = text.indexOf(linkText)
        append(text)
        addStyle(
            style = SpanStyle(
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.primary
            ),
            start = offset,
            end = offset + linkText.length
        )
        addStringAnnotation(
            tag = ANNOTATION_TAG,
            annotation = linkAnnotation.toString(),
            start = offset,
            end = offset + linkText.length
        )
    }

    Row(
        modifier = modifier.wrapContentHeight(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = imageRes),
            contentDescription = null,
        )
        ClickableText(
            text = annotatedText,
            onClick = { offset ->
                val annotations = annotatedText.getStringAnnotations(
                    tag = ANNOTATION_TAG,
                    start = offset,
                    end = offset
                )
                annotations.firstOrNull()?.let { annotation ->
                    onDownloadInfoLink(LinkAnnotation.valueOf(annotation.item))
                }
            },
            modifier = Modifier
                .wrapContentHeight()
                .padding(start = MaterialTheme.spacing.medium),
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DownloadInfoSheetPreview() {
    AppTheme {
        DownloadInfoSheet(onDownloadInfoLink = {})
    }
}

enum class LinkAnnotation {
    WIFI_SETUP,
    LIBRARY
}

private const val ANNOTATION_TAG = "LINK"
