package com.stt.android.offlinemaps.datasource

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface OfflineRegionRestApi {
    @GET("maps/catalogue")
    suspend fun getCatalogue(
        @Query("lat") latitude: Double?,
        @Query("lon") longitude: Double?,
        @Query("includeNearbyGroups") includeNearbyGroups: Boolean?,
        @Query("watchCapabilities") capabilities: String?
    ): AskoResponse<RemoteOfflineRegionCatalogue>

    @PUT("maps/library")
    @Headers("Content-Type: application/json")
    suspend fun putDownloadOrder(
        @Body downloadOrderRequest: DownloadOrderRequest,
    ): AskoResponse<RemoteOfflineRegion>

    @GET("maps/search")
    suspend fun search(
        @Query("query") searchTerm: String,
        @Query("scopes") scopes: String,
    ): AskoResponse<SearchResults>

    @GET("maps/library")
    suspend fun getLibrary(
        @Query("deviceSerialNumber") deviceSerial: String,
        @Query("watchCapabilities") capabilities: String?
    ): AskoResponse<List<RemoteLibraryOfflineRegion>>

    @GET("maps/catalogue/{regionId}")
    suspend fun getCatalogueRegion(
        @Path("regionId") regionId: String,
        @Query("deviceSerialNumber") deviceSerial: String,
        @Query("watchCapabilities") capabilities: String?
    ): AskoResponse<RemoteOfflineRegion>

    @GET("maps/library/{regionId}")
    suspend fun getLibraryRegion(
        @Path("regionId") regionId: String,
        @Query("deviceSerialNumber") deviceSerial: String,
        @Query("watchCapabilities") capabilities: String?
    ): AskoResponse<RemoteLibraryOfflineRegion>

    @DELETE("maps/library/{regionId}")
    suspend fun deleteRegion(
        @Path("regionId") regionId: String,
        @Query("deviceSerialNumber") deviceSerial: String,
        @Query("watchCapabilities") capabilities: String?
    ): AskoResponse<RemoteOfflineRegion>

    @PUT("maps/library/{regionId}/canceldelete")
    suspend fun cancelDeleteRequest(
        @Path("regionId") regionId: String,
        @Query("deviceSerialNumber") deviceSerial: String,
        @Query("watchCapabilities") capabilities: String?
    ): AskoResponse<RemoteOfflineRegion>

    @GET("maps/styles")
    suspend fun mapStyles(): AskoResponse<List<RemoteMapStyle>>

    @POST("maps/library/reset")
    suspend fun resetLibrary(
        @Body body: LibraryResetBody,
        @Query("watchCapabilities") capabilities: String?
    ): AskoResponse<List<RemoteLibraryOfflineRegion>>
}
