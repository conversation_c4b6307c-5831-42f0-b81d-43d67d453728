package com.stt.android.offlinemaps.ui

import android.text.format.Formatter
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.ListItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.FreeSpaceAvailable
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import java.util.Locale

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun OfflineRegionList(
    results: ImmutableList<OfflineRegionResult>,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    onRegionSelected: (OfflineRegionResult.OfflineRegion) -> Unit,
    onGroupSelected: (OfflineRegionResult.OfflineRegionGroup) -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion?) -> Unit,
    modifier: Modifier = Modifier,
    nearbyResults: ImmutableList<OfflineRegionResult> = persistentListOf(),
    highlight: String? = null,
    showDownloadsSection: Boolean = false,
    updateAvailableCount: Int = 0,
    freeSpaceAvailable: FreeSpaceAvailable? = null,
) {
    LazyColumn(
        modifier = modifier
    ) {
        if (showDownloadsSection) {
            item(key = "Downloads section") {
                this.DownloadsSection(
                    updateAvailableCount = updateAvailableCount,
                    freeSpaceAvailable = freeSpaceAvailable,
                    onClick = { onViewInLibrary(null) }
                )
            }
        }
        if (nearbyResults.isNotEmpty()) {
            item(key = "Nearby header item") {
                ListItem(
                    text = {
                        Text(
                            text = stringResource(id = R.string.offline_maps_nearby_title)
                                .uppercase(Locale.getDefault()),
                            style = MaterialTheme.typography.header,
                        )
                    },
                    modifier = Modifier.background(color = MaterialTheme.colors.lightGrey)
                )
            }
            items(nearbyResults, key = { result -> result.id + "_nearby" }) { result ->
                RegionListItem(
                    result = result,
                    onRegionSelected = onRegionSelected,
                    cancellingDownload = cancellingDownload,
                    requestingDownload = requestingDownload,
                    onViewInLibrary = onViewInLibrary,
                    onCancel = onCancel,
                    onRetry = onRetry,
                    onGroupSelected = onGroupSelected,
                    highlight = highlight
                )
            }
        }
        if (nearbyResults.isNotEmpty()) {
            item(key = "Countries header item") {
                ListItem(
                    text = {
                        Text(
                            text = stringResource(id = R.string.offline_maps_countries_title)
                                .uppercase(Locale.getDefault()),
                            style = MaterialTheme.typography.header,
                        )
                    },
                    modifier = Modifier.background(color = MaterialTheme.colors.lightGrey)
                )
            }
        }
        items(results, key = { result -> result.id }) { result ->
            RegionListItem(
                result = result,
                onRegionSelected = onRegionSelected,
                cancellingDownload = cancellingDownload,
                requestingDownload = requestingDownload,
                onViewInLibrary = onViewInLibrary,
                onCancel = onCancel,
                onRetry = onRetry,
                onGroupSelected = onGroupSelected,
                highlight = highlight
            )
        }
    }
}

@Composable
private fun RegionListItem(
    result: OfflineRegionResult,
    onRegionSelected: (OfflineRegionResult.OfflineRegion) -> Unit,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion) -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    onGroupSelected: (OfflineRegionResult.OfflineRegionGroup) -> Unit,
    highlight: String? = null
) {
    Box {
        when (result) {
            is OfflineRegionResult.OfflineRegion -> {
                OfflineRegionItem(
                    region = result,
                    cancellingDownload = cancellingDownload,
                    requestingDownload = requestingDownload,
                    onClick = { onRegionSelected(result) },
                    onViewInLibrary = onViewInLibrary,
                    onCancel = onCancel,
                    onRetry = onRetry,
                    modifier = Modifier.fillMaxWidth(),
                    highlight = highlight
                )
            }

            is OfflineRegionResult.OfflineRegionGroup -> {
                OfflineRegionGroupItem(
                    group = result,
                    onClick = {
                        onGroupSelected(result)
                    },
                    modifier = Modifier
                        .fillMaxWidth(),
                    highlight = highlight,
                )
            }
        }
        Divider(startIndent = MaterialTheme.spacing.medium)
    }
}

@Suppress("UnusedReceiverParameter")
@Composable
@OptIn(ExperimentalMaterialApi::class)
private fun LazyItemScope.DownloadsSection(
    updateAvailableCount: Int,
    freeSpaceAvailable: FreeSpaceAvailable?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    ListItem(
        text = {
            Box(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = stringResource(id = R.string.offline_maps_downloads)
                        .uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
                if (freeSpaceAvailable != null) {
                    Text(
                        text = stringResource(
                            id = R.string.memory_usage_free_space,
                            Formatter.formatShortFileSize(
                                LocalContext.current,
                                freeSpaceAvailable.freeSpace
                            )
                        ),
                        modifier = Modifier.align(Alignment.CenterEnd),
                        style = MaterialTheme.typography.body,
                        color = if (freeSpaceAvailable.freeSpacePercentage <= 10) {
                            MaterialTheme.colors.error
                        } else {
                            MaterialTheme.colors.secondary
                        }
                    )
                }
            }
        },
        modifier = modifier
            .background(color = MaterialTheme.colors.lightGrey)
    )
    ListItem(
        icon = {
            Box(modifier = Modifier.size(40.dp)) {
                Icon(
                    painter = painterResource(R.drawable.save_bookmark_outline),
                    contentDescription = null,
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.medium)
                        .align(Alignment.CenterStart),
                    tint = MaterialTheme.colors.secondary,
                )
            }
        },
        text = {
            Text(text = stringResource(id = R.string.offline_maps_view_map_library))
        },
        secondaryText = if (updateAvailableCount > 0) {
            {
                Text(
                    text = pluralStringResource(
                        id = R.plurals.offline_maps_available_map_updates,
                        count = updateAvailableCount,
                        updateAvailableCount,
                    )
                )
            }
        } else {
            null
        },
        trailing = {
            Icon(
                painter = painterResource(R.drawable.chevron_right),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.medium)
            )
        },
        modifier = Modifier.clickable(onClick = onClick)
    )
}

@Preview(showBackground = true)
@Composable
private fun OfflineRegionListPreview() {
    AppTheme {
        OfflineRegionList(
            results = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }.regions,
            cancellingDownload = false,
            requestingDownload = false,
            onRegionSelected = {},
            onGroupSelected = {},
            onCancel = {},
            onRetry = {},
            onViewInLibrary = {},
        )
    }
}
