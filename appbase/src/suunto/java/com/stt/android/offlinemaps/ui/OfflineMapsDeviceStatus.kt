package com.stt.android.offlinemaps.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.offlinemaps.entity.OfflineMapsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import java.util.Locale

@Composable
fun OfflineMapsDeviceStatus(
    deviceInfo: OfflineMapsDeviceInfo,
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .padding(MaterialTheme.spacing.small)
    ) {
        DeviceConnectionStatusImage(
            deviceType = deviceInfo.deviceType,
            deviceSku = deviceInfo.deviceSku,
            modifier = Modifier
                .size(55.dp)
        )
        Column(
            modifier = Modifier
                .padding(
                    start = MaterialTheme.spacing.smaller,
                    end = MaterialTheme.spacing.large
                )
        ) {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Text(
                    text = stringResource(deviceInfo.connectionStatusId)
                        .uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.bodyBold
                )
                Text(
                    text = stringResource(
                        R.string.watch_storage_state_format,
                        deviceInfo.storageUsedPercentage
                    ),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colors.secondary
                )
            }
            LinearProgressIndicator(
                progress = deviceInfo.storageUsedPercentage / 100f,
                modifier = Modifier
                    .padding(vertical = MaterialTheme.spacing.small)
                    .fillMaxWidth()
            )
            Text(
                text = stringResource(R.string.offline_maps_only_for_watch),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colors.secondary
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun OfflineMapsWatchStatusPreview() {
    AppTheme {
        OfflineMapsDeviceStatus(
            deviceInfo = OfflineMapsDeviceInfo(
                deviceType = SuuntoDeviceType.SuuntoVertical,
                deviceSku = "",
                connectionStatusId = R.string.watch_ui_connected,
                storageUsedPercentage = 75
            )
        )
    }
}
