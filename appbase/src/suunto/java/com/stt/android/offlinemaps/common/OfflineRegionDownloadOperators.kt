package com.stt.android.offlinemaps.common

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.LoggingExceptionHandler
import com.stt.android.offlinemaps.datasource.OfflineRegionDownloadRepository
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.watch.offlinemaps.domain.NotifyAreaSelectionChangedUseCase
import com.stt.android.watch.offlinemaps.domain.NotifyAreaUnderDownloadDeletedUseCase
import com.suunto.connectivity.repository.SuuntoRepositoryException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

interface OfflineRegionDownloadOperators {
    suspend fun downloadOfflineRegion(
        regionId: String,
        groupName: String? = null
    ): OfflineRegionResult.OfflineRegion

    suspend fun deleteDownload(
        region: OfflineRegionResult.OfflineRegion
    ): OfflineRegionResult.OfflineRegion

    suspend fun getMapStorageSize(): Long?
}

class OfflineRegionDownloadOperatorsDelegate
@Inject constructor(
    private val notifyAreaSelectionChangedUseCase: NotifyAreaSelectionChangedUseCase,
    private val notifyAreaUnderDownloadDeletedUseCase: NotifyAreaUnderDownloadDeletedUseCase,
    private val repository: OfflineRegionDownloadRepository,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : OfflineRegionDownloadOperators {
    override suspend fun downloadOfflineRegion(
        regionId: String,
        groupName: String?
    ): OfflineRegionResult.OfflineRegion = repository.downloadOfflineRegion(
        regionId = regionId,
        groupName = groupName
    ).also {
        notifyWatch()
    }

    override suspend fun deleteDownload(
        region: OfflineRegionResult.OfflineRegion
    ): OfflineRegionResult.OfflineRegion =
        repository.deleteDownload(
            region = region
        ).also {
            notifyWatch(regionId = region.id, ongoingDownloadCancelled = region.downloading)
        }

    override suspend fun getMapStorageSize(): Long? = repository.getMapStorageSize()

    private fun notifyWatch(
        regionId: String? = null,
        ongoingDownloadCancelled: Boolean = false
    ) {
        // Breaking structured concurrency letting the download operations and app execution to
        // continue. We should always try to notify the watch, but the operation is not critical and
        // the UI should not wait for these to complete.
        CoroutineScope(coroutinesDispatchers.io + SupervisorJob() + LoggingExceptionHandler).launch {
            runCatching {
                if (ongoingDownloadCancelled && regionId != null) {
                    runCatching { // inner catch is to support watches without the required WB API
                        notifyAreaUnderDownloadDeletedUseCase.run(regionId)
                    }.onFailure {
                        if (it is SuuntoRepositoryException && it.message?.contains("Failed status: 404") == true) {
                            Timber.d("Failed to send AreaUnderDownloadDeleted to the watch. API not supported or watch not connected.")
                        } else {
                            Timber.w(it, "Failed to send AreaUnderDownloadDeleted to the watch")
                        }
                    }
                }
                notifyAreaSelectionChangedUseCase.run()
            }
                .onSuccess { Timber.d("AreaSelectionChanged notified to watch") }
                .onFailure {
                    if (it is SuuntoRepositoryException && it.message?.contains("Failed status: 404") == true) {
                        Timber.d("Failed to send AreaSelectionChanged to the watch. Watch is not connected to the app.")
                    } else {
                        Timber.w(it, "Failed to send AreaSelectionChanged to the watch")
                    }
                }
        }
    }
}
