package com.stt.android.offlinemaps

import androidx.annotation.VisibleForTesting
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.offlinemaps.datasource.RemoteResultType
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class OfflineMapsSearchViewModel @Inject constructor(
    private val offlineRegionRepository: OfflineRegionRepository,
) : ViewModel() {

    private var offlineRegionCatalogue by mutableStateOf(OfflineRegionListData.Catalogue())

    var searching by mutableStateOf(false)
        private set

    var searchTerm by mutableStateOf("")
        private set

    var searchResults by mutableStateOf<ImmutableList<OfflineRegionResult>>(persistentListOf())

    init {
        observeSearch()
    }

    fun search(searchTerm: String, catalogue: OfflineRegionListData.Catalogue) {
        this.searchTerm = searchTerm
        this.offlineRegionCatalogue = catalogue
    }

    fun updateCatalogue(catalogue: OfflineRegionListData.Catalogue) {
        this.offlineRegionCatalogue = catalogue
    }

    fun resetSearchTerm() {
        searchTerm = ""
    }

    @OptIn(FlowPreview::class)
    @VisibleForTesting
    fun observeSearch() {
        snapshotFlow {
            searchTerm to offlineRegionCatalogue
        }.onEach { (searchTerm, _) ->
            searching = searchTerm.isNotBlank()
        }.filterNot { (searchTerm, catalogue) ->
            searchTerm.isBlank() || catalogue.groups.isEmpty()
        }
            .debounce(300)
            .mapLatest { (searchTerm, catalogue) ->
                runSuspendCatching {
                    val regionMap = catalogue.groups
                        .flatMap { it.regions }
                        .associateBy { it.id }
                    val groups = catalogue.groups
                    offlineRegionRepository.search(searchTerm)
                        .mapNotNull { searchResult ->
                            if (searchResult.type == RemoteResultType.REGION) {
                                regionMap[searchResult.id]
                            } else {
                                groups.firstOrNull { it.id == searchResult.id }
                            }
                        }.toPersistentList()
                }.getOrElse { e ->
                    Timber.w(e, "Failed to search with: $searchTerm")
                    persistentListOf()
                }
            }.onEach {
                searchResults = it
                searching = false
            }
            .launchIn(viewModelScope)
    }
}
