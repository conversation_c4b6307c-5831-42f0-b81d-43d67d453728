package com.stt.android.offlinemaps

import android.content.Context
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.core.content.edit
import androidx.core.net.toUri
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.IntentFactory
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.intentresolver.IntentKey
import com.stt.android.intentresolver.LibraryTab
import com.stt.android.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.offlinemaps.ui.LinkAnnotation
import com.stt.android.utils.STTConstants
import com.stt.android.watch.wifi.WifiNetworksActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OfflineMapsSelectionActivity : AppCompatActivity() {
    private val viewModel: OfflineMapsSelectionViewModel by viewModels()

    @Inject
    lateinit var intentFactory: IntentFactory

    @Inject
    lateinit var offlineMapsAnalytics: OfflineMapsAnalytics

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val openDownloadInfo = intent.getBooleanExtra(KEY_SHOW_DOWNLOAD_INFO, false).also {
            intent.removeExtra(KEY_SHOW_DOWNLOAD_INFO)
        }

        setContentWithM3Theme {
            AppTheme { // TODO We need this until everything has been migrated to M3.
                val navController = rememberNavController()
                NavHost(
                    navController = navController,
                    startDestination = OfflineMapsSelectionNavigation.OFFLINE_REGION_GROUPS_ROUTE,
                    modifier = Modifier.fillMaxSize(),
                    enterTransition = {
                        slideIntoContainer(
                            towards = AnimatedContentTransitionScope.SlideDirection.Left,
                            animationSpec = tween(),
                        )
                    },
                    exitTransition = {
                        slideOutOfContainer(
                            towards = AnimatedContentTransitionScope.SlideDirection.Left,
                            animationSpec = tween(),
                        )
                    },
                    popEnterTransition = {
                        slideIntoContainer(
                            towards = AnimatedContentTransitionScope.SlideDirection.Right,
                            animationSpec = tween(),
                        )
                    },
                    popExitTransition = {
                        slideOutOfContainer(
                            towards = AnimatedContentTransitionScope.SlideDirection.Right,
                            animationSpec = tween(),
                        )
                    }
                ) {
                    buildNavigationGraph(
                        navController = navController,
                        onClose = ::finishActivity,
                        openLibrary = ::navigateToLibrary,
                        openWifiSetup = ::navigateToWifiSetup,
                        openOsmDisclaimer = ::openOsmDisclaimer,
                        onDownloadInfoLink = ::navigateToDownloadInfoLink,
                        openDownloadInfo = openDownloadInfo,
                        viewModel = viewModel,
                    )
                }
            }
        }
    }

    private fun finishActivity(userStartedRegionDownload: Boolean) {
        if (userStartedRegionDownload) {
            setResult(RESULT_OK)
        }
        finish()
    }

    private fun navigateToLibrary(scrollToRegionId: String?) {
        offlineMapsAnalytics.trackMapsLibraryScreen(
            source = AnalyticsPropertyValue.MapsLibraryScreenSource.DOWNLOAD_MAP_SCREEN,
            downloadedRegions = viewModel.offlineRegionCatalogue.groups
                .flatMap { it.regions }
                .count { it.downloaded }
        )
        val intent = intentFactory.createIntent(
            this,
            IntentKey.Library(
                initialTab = LibraryTab.OFFLINE_MAPS,
                scrollToRegionId = scrollToRegionId
            )
        )
        startActivity(intent.addFlags(FLAG_ACTIVITY_CLEAR_TOP))
    }

    private fun openOsmDisclaimer() {
        @Suppress("UnsafeImplicitIntentLaunch")
        startActivity(
            Intent(Intent.ACTION_VIEW).apply {
                data = getString(R.string.osm_disclaimer_url).toUri()
            }
        )
    }

    private fun navigateToDownloadInfoLink(annotation: LinkAnnotation) {
        startActivity(
            when (annotation) {
                LinkAnnotation.WIFI_SETUP -> WifiNetworksActivity.newStartIntent(this)
                LinkAnnotation.LIBRARY -> intentFactory.createIntent(
                    this,
                    IntentKey.Library(
                        initialTab = LibraryTab.OFFLINE_MAPS,
                        scrollToRegionId = null
                    )
                )
            }
        )
    }

    private fun navigateToWifiSetup(toAddNetwork: Boolean) {
        startActivity(
            if (toAddNetwork) {
                WifiNetworksActivity.newStartIntentToAddNetwork(this)
            } else {
                WifiNetworksActivity.newStartIntent(this)
            }
        )
    }

    companion object {
        private const val KEY_SHOW_DOWNLOAD_INFO = "KEY_SHOW_DOWNLOAD_INFO"

        fun newStartIntent(
            context: Context,
            analyticsSource: String,
            offlineMapsAnalytics: OfflineMapsAnalytics,
        ): Intent {
            offlineMapsAnalytics.trackDownloadMapsScreen(analyticsSource)
            return Intent(context, OfflineMapsSelectionActivity::class.java).apply {
                // Download tips should be shown automatically when entering this activity for the
                // first time excluding navigation from the onboarding flow.
                if (analyticsSource != AnalyticsPropertyValue.DownloadMapsScreenSource.ONBOARDING_FLOW) {
                    val suuntoPrefs = context.getSharedPreferences(
                        STTConstants.SuuntoPreferences.PREFS_NAME,
                        MODE_PRIVATE,
                    )
                    val showTips = suuntoPrefs.getBoolean(KEY_SHOW_DOWNLOAD_INFO, true)
                    putExtra(KEY_SHOW_DOWNLOAD_INFO, showTips)
                    if (showTips) {
                        suuntoPrefs.edit { putBoolean(KEY_SHOW_DOWNLOAD_INFO, false) }
                    }
                }
            }
        }
    }
}
