package com.stt.android.offlinemaps.ui

import android.app.Activity.RESULT_CANCELED
import android.text.format.Formatter
import android.view.Gravity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.BottomSheetValue
import androidx.compose.material.ChipDefaults
import androidx.compose.material.DropdownMenu
import androidx.compose.material.DropdownMenuItem
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FilterChip
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.rememberBottomSheetScaffoldState
import androidx.compose.material.rememberBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.coil.placeholder
import com.stt.android.compose.layout.CenteringBottomSheetScaffold
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodySmallBold
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.numbers2
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.BottomSheetHandle
import com.stt.android.compose.widgets.InfoDialog
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.maps.SuuntoBitmapResourceDescriptor
import com.stt.android.maps.SuuntoCameraOptions
import com.stt.android.maps.SuuntoCameraUpdateNewLatLngBounds
import com.stt.android.maps.SuuntoCameraUpdateNewPosition
import com.stt.android.maps.SuuntoChinaOfflineRegion
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineMapRegionAndSelectState
import com.stt.android.offlinemaps.entity.OfflineRegionMapStyle
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.ui.map.MapHelper
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

@Composable
fun OfflineRegionPreviewScreen(
    region: OfflineRegionResult.OfflineRegion,
    mapStyles: ImmutableList<OfflineRegionMapStyle>,
    onDownloadRegion: (OfflineRegionResult.OfflineRegion) -> Unit,
    onOsmDisclaimer: () -> Unit,
    onDeleteRegion: (OfflineRegionResult.OfflineRegion) -> Unit,
    navigateUp: (Int) -> Unit,
    modifier: Modifier = Modifier,
    onSelectRegion: (String) -> Unit = {},
    allChinaRegionsAndSelectState: ImmutableList<OfflineMapRegionAndSelectState> = persistentListOf(),
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion> = persistentListOf(),
    onLoadAllChinaOfflineRegionSuccess: (List<SuuntoChinaOfflineRegion>, List<SuuntoMarker>) -> Unit = { _: List<SuuntoChinaOfflineRegion>, _: List<SuuntoMarker> -> },
    watchStorageFull: Boolean = false,
    onWatchStorageFullDialogDismissRequest: () -> Unit = {},
    onMapScaleListener: (Float) -> Unit = {}
) {
    val context = LocalContext.current
    var sheetHeight by rememberSaveable { mutableIntStateOf(0) }
    var mapHeight by rememberSaveable { mutableIntStateOf(0) }
    val bottomSheetState = rememberBottomSheetState(BottomSheetValue.Collapsed)
    val mapPadding = with(LocalDensity.current) { MaterialTheme.spacing.small.toPx().toInt() }
    var menuExpanded by rememberSaveable { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    var showStyleInfo by rememberSaveable { mutableStateOf(false) }
    var showSelectRegionInfo by rememberSaveable { mutableStateOf(false) }

    BackHandler {
        if (showStyleInfo || showSelectRegionInfo) {
            coroutineScope.launch {
                bottomSheetState.collapse()
                showStyleInfo = false
                showSelectRegionInfo = false
                bottomSheetState.expand()
            }
        } else {
            navigateUp(RESULT_CANCELED)
        }
    }

    CenteringBottomSheetScaffold(
        modifier = modifier,
        bottomSheetModifier = Modifier
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.medium
            )
            .verticalScroll(rememberScrollState())
            .onGloballyPositioned {
                sheetHeight = it.size.height
            },
        topBar = {
            OfflineMapsTopBar(
                titleText = region.name,
                onUpPressed = { navigateUp(RESULT_CANCELED) },
                actions = {
                    if (region.downloaded) {
                        IconButton(onClick = {
                            menuExpanded = true
                        }) {
                            Icon(
                                imageVector = Icons.Default.MoreVert,
                                contentDescription = null,
                                tint = MaterialTheme.colors.secondary
                            )
                        }

                        DropdownMenu(
                            expanded = menuExpanded,
                            onDismissRequest = {
                                menuExpanded = false
                            },
                            modifier = Modifier.widthIn(min = 150.dp)
                        ) {
                            DropdownMenuItem(
                                onClick = onOsmDisclaimer,
                                enabled = true
                            ) {
                                Text(
                                    text = stringResource(id = R.string.about_osm_maps),
                                )
                            }
                            DropdownMenuItem(
                                onClick = { onDeleteRegion(region) },
                                enabled = true
                            ) {
                                Text(
                                    text = stringResource(id = R.string.delete_map),
                                )
                            }
                        }
                    } else {
                        IconButton(onClick = onOsmDisclaimer) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_info_outline),
                                contentDescription = null,
                                tint = MaterialTheme.colors.primary
                            )
                        }
                    }
                }
            )
        },
        scaffoldState = rememberBottomSheetScaffoldState(
            bottomSheetState = bottomSheetState
        ),
        sheetPeekHeight = MaterialTheme.spacing.xlarge,
        sheetContent = {
            if (showStyleInfo) {
                MapStylesInfoSheet(mapStyles = getRegionMapStyles(region, mapStyles))
            } else if (showSelectRegionInfo) {
                SelectRegionsInfoSheet()
            } else {
                OfflineRegionSheetContent(
                    region = region,
                    mapStyles = mapStyles,
                    onDownloadRegion = onDownloadRegion,
                    onStyleInfo = {
                        coroutineScope.launch {
                            bottomSheetState.collapse()
                            showStyleInfo = true
                            bottomSheetState.expand()
                        }
                    },
                    selectedChinaRegions = selectedChinaRegions,
                    onSelectRegionInfo = {
                        coroutineScope.launch {
                            bottomSheetState.collapse()
                            showSelectRegionInfo = true
                            bottomSheetState.expand()
                        }
                    }
                )
            }
        }
    ) { internalPadding ->
        ContentCenteringColumn(Modifier.padding(internalPadding)) {
            Surface {
                if (watchStorageFull) {
                    InfoDialog(
                        title = stringResource(id = R.string.download_request_failed_generic_text),
                        text = stringResource(id = R.string.watch_storage_full),
                        confirmButtonText = stringResource(R.string.ok),
                        onDismissRequest = {
                            onWatchStorageFullDialogDismissRequest.invoke()
                        },
                        onConfirm = {
                            onWatchStorageFullDialogDismissRequest.invoke()
                        }
                    )
                }
                LaunchedEffect(key1 = Unit) {
                    delay(500) // Give time for navigation animation to complete
                    bottomSheetState.expand()
                }
                SuuntoMap(
                    modifier = Modifier
                        .fillMaxSize()
                        .onGloballyPositioned {
                            mapHeight = it.size.height
                        },
                    mapOptions = SuuntoMapOptions(
                        mapsProvider = MapboxMapsProvider.NAME,
                        uiAttribution = false
                    )
                ) { map ->
                    // Move Mapbox logo to top right corner
                    map.getUiSettings().setLogoPosition(Gravity.TOP or Gravity.END)
                    if (allChinaRegionsAndSelectState.isNotEmpty()) {
                        if (!region.boundaryUrl.isNullOrBlank()) {
                            MapHelper.addChinaOfflineRegionOverlay(
                                map,
                                region.id,
                                region.boundaryUrl,
                                ContextCompat.getColor(context, R.color.suunto_blue),
                            )
                        }
                        if (!region.maskUrl.isNullOrBlank()) {
                            MapHelper.addOfflineRegionMaskOverlay(map, region.maskUrl)
                        }

                        val layerIds = mutableListOf<String>()
                        val allChinaOfflineRegions = mutableListOf<SuuntoChinaOfflineRegion>()
                        val regionMarkers = mutableListOf<SuuntoMarkerOptions>()
                        allChinaRegionsAndSelectState.forEachIndexed { _, value ->
                            if (!value.region.boundaryUrl.isNullOrBlank()) {
                                val addChinaOfflineRegionOverlay =
                                    MapHelper.addChinaOfflineRegionOverlay(
                                        map,
                                        value.region.id,
                                        value.region.boundaryUrl,
                                        if (value.region.downloadAvailable) {
                                            ContextCompat.getColor(context, R.color.near_white)
                                        } else {
                                            ContextCompat.getColor(context, R.color.cloudy_grey)
                                        },
                                    )
                                value.region.centerPoint?.let {
                                    val icon =
                                        if (value.region.downloadRequested || value.region.downloading) {
                                            SuuntoBitmapResourceDescriptor(
                                                context,
                                                R.drawable.ic_downloading,
                                                true
                                            )
                                        } else if (value.region.downloaded) {
                                            SuuntoBitmapResourceDescriptor(
                                                context,
                                                R.drawable.ic_downloaded,
                                                true
                                            )
                                        } else {
                                            null
                                        }
                                    icon?.apply {
                                        regionMarkers.add(
                                            SuuntoMarkerOptions().position(it).icon(icon)
                                        )
                                    }
                                }
                                allChinaOfflineRegions.add(addChinaOfflineRegionOverlay)
                                val mask = addChinaOfflineRegionOverlay.mask
                                if (value.region.downloadAvailable && mask != null) {
                                    layerIds.add(mask.options.layerTypeOptions.getLayerId())
                                }
                            }
                        }
                        val markers = map.addMarkers(regionMarkers)
                        onLoadAllChinaOfflineRegionSuccess.invoke(allChinaOfflineRegions, markers)
                        map.addOnMapClickListener(object : SuuntoMap.OnMapClickListener {
                            override fun onMapClick(latLng: LatLng, placeName: String?) {
                                coroutineScope.launch {
                                    val clickLayerSourceIds = map.getClickLayerSourceIds(
                                        latLng,
                                        layerIds
                                    )
                                    if (clickLayerSourceIds.isNotEmpty()) {
                                        onSelectRegion.invoke(clickLayerSourceIds.first())
                                    }
                                }
                            }
                        })
                        map.addOnScaleListener(object : SuuntoMap.OnScaleListener {
                            override fun onScaleBegin() {
                                // do nothing
                            }

                            override fun onScaleEnd() {
                                // scale markers
                                onMapScaleListener(map.getCameraPosition()?.zoom ?: 1f)
                            }
                        })
                    } else {
                        if (!region.maskUrl.isNullOrBlank()) {
                            MapHelper.addOfflineRegionMaskOverlay(map, region.maskUrl)
                        }
                        if (!region.boundaryUrl.isNullOrBlank()) {
                            MapHelper.addOfflineRegionOverlay(
                                map,
                                region.boundaryUrl,
                                region.id,
                                ContextCompat.getColor(context, R.color.suunto_blue)
                            )
                        }
                    }
                    setCamera(region, map, mapPadding, sheetHeight, mapHeight)
                }
            }
        }
    }
}

private fun setCamera(
    region: OfflineRegionResult.OfflineRegion,
    map: SuuntoMap?,
    mapPadding: Int,
    sheetHeight: Int,
    mapHeight: Int,
) {
    val bottomPadding = if (mapHeight < sheetHeight) mapPadding else sheetHeight
    map?.setPadding(mapPadding, mapPadding, mapPadding, bottomPadding)
    if (map != null && region.bounds != null) {
        map.moveCamera(
            SuuntoCameraUpdateNewLatLngBounds(
                region.bounds,
                mapPadding
            )
        )
        // Reset map padding to avoid issue when the user zooms out the map
        resetMapPadding(map, mapPadding)
    }
}

private fun resetMapPadding(map: SuuntoMap, mapPadding: Int) {
    map.setPadding(mapPadding, mapPadding, mapPadding, mapPadding)
    val position = map.getCameraPosition()
    if (position != null) {
        map.moveCamera(
            SuuntoCameraUpdateNewPosition(
                SuuntoCameraOptions.fromCameraPosition(position)
            )
        )
    }
}

@Composable
private fun MapStylesInfoSheet(
    mapStyles: ImmutableList<OfflineRegionMapStyle>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
    ) {
        BottomSheetHandle()
        Text(
            text = stringResource(id = R.string.offline_maps_watch_map_styles),
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.small,
                bottom = MaterialTheme.spacing.large
            ),
        )
        mapStyles.forEach { mapStyle ->
            MapStyleDescription(
                mapStyle = mapStyle,
                modifier = Modifier.padding(bottom = MaterialTheme.spacing.medium)
            )
        }
    }
}

@Composable
private fun SelectRegionsInfoSheet(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
    ) {
        BottomSheetHandle()
        Text(
            text = stringResource(id = R.string.default_selected_region),
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.small,
                bottom = MaterialTheme.spacing.large
            ),
        )
        Text(
            text = stringResource(id = R.string.adjacent_region_content),
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

@Composable
private fun OfflineRegionSheetContent(
    region: OfflineRegionResult.OfflineRegion,
    mapStyles: ImmutableList<OfflineRegionMapStyle>,
    onDownloadRegion: (OfflineRegionResult.OfflineRegion) -> Unit,
    onStyleInfo: () -> Unit,
    onSelectRegionInfo: () -> Unit,
    modifier: Modifier = Modifier,
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion> = persistentListOf(),
) = Column(modifier = modifier) {
    BottomSheetHandle()
    if (region.batchDownloadAllowed == true) {
        ChinaOfflineRegionInfo(
            currentRegion = region,
            selectedChinaRegions = selectedChinaRegions,
            onSelectRegionInfo = onSelectRegionInfo
        )
    }
    OfflineMapValues(
        currentRegion = region,
        modifier = Modifier.padding(top = MaterialTheme.spacing.small),
        selectedChinaRegions = selectedChinaRegions
    )
    MapStyles(
        region = region,
        mapStyles = mapStyles,
        onStyleInfo = onStyleInfo,
        modifier = Modifier.padding(top = MaterialTheme.spacing.medium)
    )
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.xlarge)
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_watch_basic_outline),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium)
        )
        Text(
            text = stringResource(
                id = if (region.downloaded) {
                    R.string.offline_maps_only_for_watch
                } else {
                    R.string.offline_map_will_be_downloaded_to_watch_only
                }
            ),
            style = MaterialTheme.typography.body,
            modifier = Modifier
                .padding(start = MaterialTheme.spacing.medium)
                .align(CenterVertically)
        )
    }
    if (region.downloadAvailable) {
        PrimaryButton(
            onClick = { onDownloadRegion(region) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.spacing.large)
        ) {
            Icon(
                painter = painterResource(R.drawable.download_fill),
                contentDescription = null
            )
            Spacer(
                modifier = Modifier.size(MaterialTheme.spacing.small)
            )
            Text(
                text = stringResource(R.string.offline_maps_download_for_watch)
                    .uppercase(Locale.getDefault()),
            )
        }
    }
}

@Composable
private fun MapStyles(
    region: OfflineRegionResult.OfflineRegion,
    mapStyles: ImmutableList<OfflineRegionMapStyle>,
    onStyleInfo: () -> Unit,
    modifier: Modifier = Modifier
) {
    val styles = getRegionMapStyles(region, mapStyles)
    if (styles.isNotEmpty()) {
        Column(
            modifier = modifier
        ) {
            Row(
                modifier = Modifier.padding(bottom = MaterialTheme.spacing.small),
                verticalAlignment = CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.offline_maps_watch_map_styles)
                        .uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.bodySmallBold,
                )
                IconButton(onClick = onStyleInfo) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_info_outline),
                        contentDescription = null,
                        modifier = Modifier.size(MaterialTheme.iconSizes.small),
                        tint = MaterialTheme.colors.primaryVariant
                    )
                }
            }
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
            ) {
                items(styles, key = { style -> style.id }) { style ->
                    MapStyle(style)
                }
            }
        }
    }
}

private fun getRegionMapStyles(
    region: OfflineRegionResult.OfflineRegion,
    mapStyles: ImmutableList<OfflineRegionMapStyle>
) = region.styleIds
    .mapNotNull { styleId -> mapStyles.firstOrNull { styleId == it.id } }
    .toPersistentList()

@Composable
fun MapStyle(
    mapStyle: OfflineRegionMapStyle,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(mapStyle.imageUrl)
                .crossfade(true)
                .apply {
                    if (LocalInspectionMode.current) {
                        placeholder(LocalContext.current, R.drawable.map_type_mapbox_terrain)
                    }
                }
                .build(),
            contentDescription = null,
            modifier = Modifier
                .size(52.dp),
            contentScale = ContentScale.Fit
        )
        Text(
            text = mapStyle.name,
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(top = MaterialTheme.spacing.small)
        )
    }
}

@Composable
fun MapStyleDescription(
    mapStyle: OfflineRegionMapStyle,
    modifier: Modifier = Modifier
) {
    Row(modifier = modifier) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(mapStyle.imageUrl)
                .crossfade(true)
                .apply {
                    if (LocalInspectionMode.current) {
                        placeholder(LocalContext.current, R.drawable.map_type_mapbox_terrain)
                    }
                }
                .build(),
            contentDescription = null,
            modifier = Modifier
                .padding(end = MaterialTheme.spacing.medium)
                .size(52.dp),
            contentScale = ContentScale.Fit
        )
        Column {
            Text(
                text = mapStyle.name,
                style = MaterialTheme.typography.bodyLargeBold,
            )
            Text(
                text = mapStyle.description,
                style = MaterialTheme.typography.bodyLarge,
            )
        }
    }
}

@Composable
fun OfflineMapValues(
    currentRegion: OfflineRegionResult.OfflineRegion,
    modifier: Modifier = Modifier,
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion> = persistentListOf(),
) {
    val context = LocalContext.current
    var regionAreaSize = currentRegion.area ?: 0.0
    var regionFileSize = currentRegion.size ?: 0L
    val regionAreaUnitResId = currentRegion.areaUnitRes ?: 0
    selectedChinaRegions.forEach {
        regionAreaSize += it.area ?: 0.0
        regionFileSize += it.size ?: 0L
    }
    val (fileSize, fileSizeUnit) = rememberSaveable(regionAreaSize) {
        if (regionFileSize != 0L) {
            with(Formatter.formatShortFileSize(context, regionFileSize).split(' ')) {
                first() to last()
            }
        } else {
            "" to ""
        }
    }

    Box(modifier = modifier.fillMaxWidth()) {
        if (regionAreaSize != 0.0 && regionAreaUnitResId != 0) {
            MapValue(
                value = String.format(Locale.ROOT, "%.1f", regionAreaSize),
                unit = stringResource(id = regionAreaUnitResId),
                label = stringResource(id = R.string.area_size)
            )
        }
        if (fileSize.isNotBlank()) {
            MapValue(
                value = fileSize,
                unit = fileSizeUnit,
                label = stringResource(id = R.string.file_size),
                modifier = Modifier.align(Alignment.CenterEnd),
                valueAlignment = Alignment.End,
                labelTextAlignment = Alignment.End
            )
        }
    }
}

@Composable
private fun MapValue(
    value: String,
    unit: String,
    label: String,
    modifier: Modifier = Modifier,
    valueAlignment: Alignment.Horizontal = Alignment.Start,
    labelTextAlignment: Alignment.Horizontal = Alignment.Start
) {
    Column(
        modifier = modifier,
        horizontalAlignment = valueAlignment
    ) {
        Row {
            Text(
                text = value,
                style = MaterialTheme.typography.numbers2,
                modifier = Modifier.alignByBaseline()
            )
            Text(
                text = unit,
                style = MaterialTheme.typography.body,
                modifier = Modifier
                    .alignByBaseline()
                    .padding(start = MaterialTheme.spacing.xsmall)
            )
        }
        Text(
            text = label,
            style = MaterialTheme.typography.body,
            modifier = Modifier.align(labelTextAlignment)
        )
    }
}

@Composable
private fun ChinaOfflineRegionInfo(
    currentRegion: OfflineRegionResult.OfflineRegion,
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion>,
    onSelectRegionInfo: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(verticalAlignment = CenterVertically) {
            Text(
                text = stringResource(id = R.string.default_selected_region)
                    .uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodySmallBold,
            )
            IconButton(onClick = onSelectRegionInfo) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_info_outline),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                    tint = MaterialTheme.colors.primaryVariant
                )
            }
        }
        Row(
            modifier = Modifier
                .padding(bottom = MaterialTheme.spacing.medium)
                .horizontalScroll(rememberScrollState()),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
        ) {
            ChinaRegionChip(
                selectedRegion = currentRegion,
            )
            selectedChinaRegions.forEach {
                ChinaRegionChip(
                    selectedRegion = it,
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun ChinaRegionChip(
    selectedRegion: OfflineRegionResult.OfflineRegion,
    modifier: Modifier = Modifier
) {
    FilterChip(
        selected = true,
        onClick = {},
        border = BorderStroke(ChipDefaults.OutlinedBorderSize, Color.Transparent),
        colors = ChipDefaults.filterChipColors(
            selectedBackgroundColor = MaterialTheme.colors.primary,
            selectedContentColor = MaterialTheme.colors.nearWhite,
            backgroundColor = Color.White,
            contentColor = MaterialTheme.colors.nearBlack
        )
    ) {
        Text(
            text = stringResource(
                id = R.string.offline_maps_region_name,
                selectedRegion.name.takeLast(2)
            ),
            style = MaterialTheme.typography.body
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun MapStylesInfoSheetPreview() {
    AppTheme {
        MapStylesInfoSheet(
            mapStyles = listOf(
                OfflineRegionMapStyle(id = "", imageUrl = "", name = "Outdoor", description = "Tailored for hiking and outdoor activities. Tailored for hiking and outdoor activities"),
                OfflineRegionMapStyle(id = "", imageUrl = "", name = "Outdoor", description = ""),
                OfflineRegionMapStyle(id = "", imageUrl = "", name = "Outdoor", description = ""),
            ).toPersistentList(),
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        )
    }
}

@Preview
@Composable
private fun OfflineRegionPreviewPreview() {
    AppTheme {
        OfflineRegionPreviewScreen(
            region = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }.regions.first(),
            mapStyles = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_MAP_STYLES,
            onDownloadRegion = {},
            onOsmDisclaimer = {},
            onDeleteRegion = {},
            navigateUp = {},
            onSelectRegion = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OfflineRegionBottomSheetPreview() {
    AppTheme {
        OfflineRegionSheetContent(
            region = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }.regions.first { it.downloadAvailable },
            mapStyles = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_MAP_STYLES,
            onDownloadRegion = {},
            onStyleInfo = {},
            onSelectRegionInfo = {}
        )
    }
}
