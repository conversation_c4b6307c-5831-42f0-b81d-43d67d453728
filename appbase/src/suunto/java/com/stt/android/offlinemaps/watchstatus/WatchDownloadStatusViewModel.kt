package com.stt.android.offlinemaps.watchstatus

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.offlinemaps.watchstatus.entities.WatchDownloadStatus
import com.stt.android.watch.device.GetUsbCableStateUseCase
import com.stt.android.watch.wifi.domain.GetSavedWifiNetworksCountUseCase
import com.stt.android.watch.wifi.domain.GetWifiEnabledUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class WatchDownloadStatusViewModel @Inject constructor(
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val networksCountUseCase: GetSavedWifiNetworksCountUseCase,
    private val isWatchBusyUseCase: IsWatchBusyUseCase,
    private val wifiEnabledUseCase: GetWifiEnabledUseCase,
    private val getUsbCableStateUseCase: GetUsbCableStateUseCase,
) : ViewModel() {

    private var watchStatesJob: Job? = null

    private var isWatchConnected by mutableStateOf(false)

    var status by mutableStateOf(WatchDownloadStatus.UNKNOWN)
        private set

    init {
        observeIsWatchConnected()
    }

    private fun observeIsWatchConnected() {
        viewModelScope.launch {
            isWatchConnectedUseCase.isWatchConnected()
                .catch { Timber.w(it, "Unable to get watch connection state.") }
                .distinctUntilChanged()
                .collectLatest {
                    isWatchConnected = it
                    if (isWatchConnected) {
                        observeWatchStates()
                    } else {
                        watchStatesJob?.cancel()
                        status = WatchDownloadStatus.NOT_CONNECTED_TO_APP
                    }
                }
        }
    }

    private fun observeWatchStates() {
        watchStatesJob = viewModelScope.launch {
            combine(
                isWatchBusyUseCase.isWatchBusy()
                    .catch { Timber.w(it, "Failed to get watch busy state.") },
                wifiEnabledUseCase.run()
                    .catch { Timber.w(it, "Failed to get wifi enabled state.") },
                networksCountUseCase.run()
                    .catch { Timber.w(it, "Failed to get saved networks count.") },
                getUsbCableStateUseCase.run()
                    .map { it.connected }
                    .catch { Timber.w(it, "Failed to get usb cable state.") }
            ) { isBusy, wifiEnabled, networkCount, cableConnected ->
                val status = when {
                    !isWatchConnected -> WatchDownloadStatus.NOT_CONNECTED_TO_APP
                    else -> when {
                        isBusy -> WatchDownloadStatus.BUSY
                        !wifiEnabled -> WatchDownloadStatus.WIRELESS_NETWORK_DISABLED
                        networkCount == 0 -> WatchDownloadStatus.WIFI_SETUP_MISSING
                        !cableConnected -> WatchDownloadStatus.NOT_CHARGING
                        else -> WatchDownloadStatus.READY
                    }
                }
                status
            }
                .catch { Timber.w(it, "Failed to get watch download status.") }
                .distinctUntilChanged()
                .collectLatest {
                    status = it
                }
        }
    }
}
