package com.stt.android.offlinemaps.ui

import android.app.Activity
import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.stt.android.R
import com.stt.android.core.R as CoreR
import com.stt.android.compose.layout.CenteringModalBottomSheetLayout
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.compose.widgets.InfoDialog
import com.stt.android.compose.widgets.SearchBar
import com.stt.android.offlinemaps.OfflineMapsSearchViewModel
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.FreeSpaceAvailable
import com.stt.android.offlinemaps.entity.OfflineRegionDownloadError
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.watchstatus.ui.WatchDownloadStatus
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.dropWhile
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Suppress("KotlinConstantConditions")
@Composable
fun OfflineRegionsScreen(
    listData: OfflineRegionListData,
    catalogue: OfflineRegionListData.Catalogue,
    loading: Boolean,
    watchConnected: Boolean,
    wifiEnabled: Boolean,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    downloadError: OfflineRegionDownloadError?,
    savedWifiNetworksCount: Int,
    updateAvailableCount: Int,
    freeSpaceAvailable: FreeSpaceAvailable?,
    wifiDisabledDismissed: Boolean,
    wifiSetupDismissed: Boolean,
    batteryTipDismissed: Boolean,
    onOfflineRegionGroupSelected: (OfflineRegionResult.OfflineRegionGroup) -> Unit,
    onOfflineRegionSelected: (OfflineRegionResult.OfflineRegion) -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion?) -> Unit,
    onEnableWifi: () -> Unit,
    onWifiSetup: () -> Unit,
    onDownloadInfoLink: (LinkAnnotation) -> Unit,
    onDownloadFailedDismiss: () -> Unit,
    onWifiDisabledDismiss: () -> Unit,
    onWifiSetupDismiss: () -> Unit,
    onBatteryTipDismiss: () -> Unit,
    onEnableSearchMode: () -> Unit,
    navigateUp: () -> Unit,
    modifier: Modifier = Modifier,
    openDownloadInfo: Boolean = false,
    searchViewModel: OfflineMapsSearchViewModel = hiltViewModel(),
) {
    var inSearchMode by rememberSaveable { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val bottomSheetState =
        rememberModalBottomSheetState(ModalBottomSheetValue.Hidden, skipHalfExpanded = true)
    val coroutineScope = rememberCoroutineScope()

    var showInfo by rememberSaveable { mutableStateOf(openDownloadInfo) }

    val name = when (listData) {
        is OfflineRegionListData.Group -> listData.group.name
        is OfflineRegionListData.Catalogue -> stringResource(R.string.offline_maps_download_maps_button)
    }
    val results = when (listData) {
        is OfflineRegionListData.Group -> listData.group.regions
        is OfflineRegionListData.Catalogue -> listData.groups
    }

    val nearbyResults = when (listData) {
        is OfflineRegionListData.Group -> persistentListOf()
        is OfflineRegionListData.Catalogue -> listData.nearby
    }

    // Suggest setting up wifi on the watch in case of pending downloads
    val downloadQueueCount = when (listData) {
        is OfflineRegionListData.Group -> listData.group.regions.count { it.downloadRequested }
        is OfflineRegionListData.Catalogue -> listData.groups.flatMap { it.regions }.count { it.downloadRequested }
    }

    var showWifiDisabledDialog by rememberSaveable(
        downloadQueueCount,
        wifiDisabledDismissed,
        watchConnected,
        wifiEnabled
    ) { mutableStateOf(downloadQueueCount > 0 && !wifiDisabledDismissed && watchConnected && !wifiEnabled) }

    var showSetupWifiDialog by rememberSaveable(
        downloadQueueCount,
        savedWifiNetworksCount,
        wifiSetupDismissed,
        watchConnected,
        wifiEnabled,
    ) { mutableStateOf(downloadQueueCount > 0 && savedWifiNetworksCount == 0 && !wifiSetupDismissed && watchConnected && wifiEnabled) }

    var showBatteryTipDialog by rememberSaveable(
        wifiEnabled,
        downloadQueueCount,
        savedWifiNetworksCount,
        batteryTipDismissed
    ) { mutableStateOf(wifiEnabled && downloadQueueCount > 0 && savedWifiNetworksCount > 0 && !batteryTipDismissed) }

    LaunchedEffect(catalogue) {
        if (inSearchMode) {
            searchViewModel.updateCatalogue(catalogue)
        }
    }

    if (showWifiDisabledDialog) {
        ConfirmationDialog(
            title = stringResource(R.string.wifi_disabled_title),
            text = stringResource(R.string.wifi_disabled_text),
            cancelButtonText = stringResource(R.string.not_now),
            confirmButtonText = stringResource(R.string.enable),
            onDismissRequest = {
                onWifiDisabledDismiss()
                showWifiDisabledDialog = false
            },
            onConfirm = {
                onWifiDisabledDismiss()
                onEnableWifi()
                showWifiDisabledDialog = false
            }
        )
    }
    if (showSetupWifiDialog) {
        ConfirmationDialog(
            title = stringResource(R.string.setup_wifi_query_title),
            text = stringResource(R.string.setup_wifi_query_text),
            cancelButtonText = stringResource(R.string.not_now),
            confirmButtonText = stringResource(R.string.setup_wifi_now),
            onDismissRequest = {
                onWifiSetupDismiss()
                showSetupWifiDialog = false
            },
            onConfirm = {
                onWifiSetupDismiss()
                onWifiSetup()
                showSetupWifiDialog = false
            }
        )
    }
    if (downloadError != null) {
        val title = stringResource(
            if (downloadError.description != null) {
                R.string.download_request_failed_generic_text
            } else {
                R.string.download_request_failed_title
            }
        )
        val text = downloadError.description
            ?: stringResource(
                R.string.download_request_failed_text,
                downloadError.regionName
            )

        InfoDialog(
            title = title,
            text = text,
            confirmButtonText = stringResource(R.string.ok),
            onDismissRequest = {
                onDownloadFailedDismiss()
            },
            onConfirm = {
                onDownloadFailedDismiss()
            }
        )
    }

    FullscreenAnimationPopup(
        visible = showBatteryTipDialog,
        onBatteryTipDismiss = {
            showBatteryTipDialog = false
            onBatteryTipDismiss()
        },
    )

    DisposableEffectOnLifecycleStart(
        LocalLifecycleOwner.current,
        ignoreChangingConfigurations = true
    ) {
        if (searchViewModel.searchTerm.isNotBlank()) {
            searchViewModel.search(searchViewModel.searchTerm, catalogue)
        }
    }

    CenteringModalBottomSheetLayout(
        sheetState = bottomSheetState,
        sheetContent = {
            DownloadInfoSheet(onDownloadInfoLink = onDownloadInfoLink)
        }
    ) {
        Scaffold(
            modifier = modifier,
            topBar = {
                OfflineMapsTopBar(
                    titleText = name,
                    onUpPressed = navigateUp,
                    actions =
                    {
                        Box {
                            IconButton(onClick = {
                                coroutineScope.launch {
                                    if (bottomSheetState.isVisible) {
                                        bottomSheetState.hide()
                                    } else {
                                        bottomSheetState.show()
                                    }
                                }
                            }) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_info_outline),
                                    contentDescription = null,
                                    tint = MaterialTheme.colors.secondary
                                )
                            }
                            Box(
                                modifier = Modifier
                                    .padding(horizontal = 12.dp, vertical = 12.dp)
                                    .size(8.dp)
                                    .clip(CircleShape)
                                    .background(colorResource(CoreR.color.functional_red))
                                    .align(Alignment.TopEnd)
                            )
                        }
                    },
                )
            }
        ) { internalPadding ->

            BackHandler {
                if (bottomSheetState.isVisible) {
                    coroutineScope.launch { bottomSheetState.hide() }
                } else {
                    navigateUp()
                }
            }

            ContentCenteringColumn(Modifier.padding(internalPadding)) {
                Surface {
                    Column(Modifier.fillMaxSize()) {
                        WatchDownloadStatus(modifier = Modifier.padding(top = MaterialTheme.spacing.medium))
                        SearchBar(
                            searching = searchViewModel.searching,
                            searchTerm = searchViewModel.searchTerm,
                            onSearchTermChange = {
                                searchViewModel.search(it, catalogue)
                            },
                            onResetSearchTerm = {
                                searchViewModel.resetSearchTerm()
                            },
                            cancelButtonText = stringResource(id = R.string.cancel),
                            showCancelButton = inSearchMode,
                            onCancel = {
                                focusManager.clearFocus()
                                inSearchMode = false
                                searchViewModel.resetSearchTerm()
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    start = MaterialTheme.spacing.medium,
                                    top = MaterialTheme.spacing.smaller,
                                    end = MaterialTheme.spacing.medium,
                                    bottom = MaterialTheme.spacing.medium
                                )
                                .onFocusChanged {
                                    if (it.hasFocus) {
                                        if (!inSearchMode) {
                                            onEnableSearchMode()
                                        }
                                        inSearchMode = true
                                    }
                                },
                            placeholder = stringResource(R.string.search_bar_placeholder_by_name),
                            focusRequester = focusRequester
                        )

                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        ) {
                            if (inSearchMode) {
                                SearchResults(
                                    searching = searchViewModel.searching,
                                    cancellingDownload = cancellingDownload,
                                    requestingDownload = requestingDownload,
                                    searchTerm = searchViewModel.searchTerm,
                                    searchResults = searchViewModel.searchResults,
                                    onOfflineRegionSelected = onOfflineRegionSelected,
                                    onOfflineRegionGroupSelected = onOfflineRegionGroupSelected,
                                    onCancel = onCancel,
                                    onRetry = onRetry,
                                    onViewInLibrary = onViewInLibrary,
                                )
                            } else {
                                if (loading) {
                                    CircularProgressIndicator(
                                        Modifier.align(Alignment.Center)
                                    )
                                } else {
                                    OfflineRegionList(
                                        results = results,
                                        cancellingDownload = cancellingDownload,
                                        requestingDownload = requestingDownload,
                                        onRegionSelected = onOfflineRegionSelected,
                                        onGroupSelected = onOfflineRegionGroupSelected,
                                        onCancel = onCancel,
                                        onRetry = onRetry,
                                        onViewInLibrary = onViewInLibrary,
                                        modifier = Modifier
                                            .fillMaxSize(),
                                        nearbyResults = nearbyResults,
                                        showDownloadsSection = listData is OfflineRegionListData.Catalogue,
                                        updateAvailableCount = updateAvailableCount,
                                        freeSpaceAvailable = freeSpaceAvailable,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }

        if (showInfo) {
            LaunchedEffect(key1 = Unit) {
                bottomSheetState.show()
            }
        }

        LaunchedEffect(Unit) {
            snapshotFlow { bottomSheetState.currentValue }
                .dropWhile { it != ModalBottomSheetValue.Expanded }
                .filter { it == ModalBottomSheetValue.Hidden }
                .collect {
                    if (it == ModalBottomSheetValue.Hidden) {
                        showInfo = false
                    }
                }
        }
    }
}

@Composable
internal fun DisposableEffectOnLifecycleStart(
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    context: Context = LocalContext.current,
    ignoreChangingConfigurations: Boolean = false,
    effect: () -> Unit
) {
    var isChangingConfigurations by rememberSaveable { mutableStateOf(false) }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    if (context is Activity && context.isChangingConfigurations) {
                        isChangingConfigurations = true
                    }
                }

                Lifecycle.Event.ON_START -> {
                    if (!isChangingConfigurations || !ignoreChangingConfigurations) {
                        effect()
                    }
                    isChangingConfigurations = false
                }

                else -> {
                    /* no-op */
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

@Preview
@Composable
private fun OfflineRegionsScreenPreview(
    @PreviewParameter(OfflineRegionsScreenParamsProvider::class) params: OfflineRegionsScreenParams
) {
    AppTheme {
        OfflineRegionsScreen(
            listData = params.listData,
            catalogue = OfflineRegionListData.Catalogue(persistentListOf(), persistentListOf()),
            loading = false,
            watchConnected = true,
            wifiEnabled = true,
            cancellingDownload = false,
            requestingDownload = false,
            downloadError = null,
            savedWifiNetworksCount = 1,
            updateAvailableCount = 6,
            freeSpaceAvailable = FreeSpaceAvailable(6000000L, 10),
            wifiDisabledDismissed = true,
            wifiSetupDismissed = true,
            batteryTipDismissed = true,
            onOfflineRegionGroupSelected = {},
            onOfflineRegionSelected = {},
            onCancel = {},
            onRetry = {},
            onViewInLibrary = {},
            onEnableWifi = {},
            onWifiSetup = {},
            onDownloadInfoLink = {},
            onDownloadFailedDismiss = {},
            onWifiDisabledDismiss = {},
            onWifiSetupDismiss = {},
            onBatteryTipDismiss = {},
            onEnableSearchMode = {},
            navigateUp = {},
        )
    }
}

private class OfflineRegionsScreenParamsProvider :
    PreviewParameterProvider<OfflineRegionsScreenParams> {
    override val values: Sequence<OfflineRegionsScreenParams> = sequenceOf(
        OfflineRegionsScreenParams(
            listData = OfflineRegionListData.Group(
                DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                    .first { it.name == "Finland" }
            )
        ),
        OfflineRegionsScreenParams(
            listData = OfflineRegionListData.Catalogue(
                groups = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
            )
        ),
    )
}

private data class OfflineRegionsScreenParams(
    val listData: OfflineRegionListData
)
