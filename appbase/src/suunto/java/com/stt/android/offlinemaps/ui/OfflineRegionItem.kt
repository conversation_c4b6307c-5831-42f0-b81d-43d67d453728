package com.stt.android.offlinemaps.ui

import android.text.format.Formatter.formatShortFileSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.ListItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.highlightedString
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.offlinemaps.datasource.DownloadOrder
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.datasource.OfflineRegionStatus
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import java.util.Locale

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun OfflineRegionItem(
    region: OfflineRegionResult.OfflineRegion,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    onClick: () -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    modifier: Modifier = Modifier,
    highlight: String? = null,
    onViewInLibrary: ((OfflineRegionResult.OfflineRegion) -> Unit)? = null,
) {
    var showCancelConfirmation by rememberSaveable { mutableStateOf(false) }

    val secondaryText = when {
        region.downloadRequested -> stringResource(id = R.string.download_requested)
        region.downloading -> stringResource(id = R.string.download_downloading)
        region.downloaded && onViewInLibrary != null -> stringResource(id = R.string.download_downloaded)
        region.downloadFailed -> stringResource(id = R.string.download_failed)
        else -> regionSecondaryText(region = region)
    }

    val secondaryTextColor: Color = when {
        region.downloadFailed -> MaterialTheme.colors.error
        region.downloaded && onViewInLibrary != null -> Color(0x1A, 0xA2, 0x43)
        else -> MaterialTheme.colors.primaryVariant
    }

    val action = getRegionAction(
        region = region,
        cancellingDownload = cancellingDownload,
        requestingDownload = requestingDownload,
        onViewInLibrary = onViewInLibrary,
        onCancel = { showCancelConfirmation = true },
        onRetry = onRetry
    )

    Column(
        modifier = modifier
            .background(color = MaterialTheme.colors.surface),
    ) {
        Row {
            @Suppress("SENSELESS_COMPARISON")
            ListItem(
                icon = {
                    Box(modifier = Modifier.size(40.dp)) {
                        Icon(
                            painter = painterResource(R.drawable.map_type_outline),
                            contentDescription = null,
                            modifier = Modifier
                                .size(MaterialTheme.iconSizes.medium)
                                .align(Alignment.CenterStart),
                            tint = MaterialTheme.colors.secondary,
                        )
                    }
                },
                text = {
                    Text(
                        highlightedString(
                            text = region.name,
                            highlight = highlight,
                            highlightStyle = SpanStyle(
                                color = MaterialTheme.colors.primary,
                                fontWeight = FontWeight.Bold
                            )
                        )
                    )
                },
                secondaryText = {
                    Text(
                        text = highlightedString(
                            text = secondaryText,
                            highlight = highlight,
                            highlightStyle = SpanStyle(
                                color = MaterialTheme.colors.primary,
                                fontWeight = FontWeight.Bold
                            )
                        ),
                        color = secondaryTextColor
                    )
                },
                trailing = if (action == null) {
                    {
                        Icon(
                            painter = painterResource(R.drawable.chevron_right),
                            contentDescription = null,
                            modifier = Modifier.size(MaterialTheme.iconSizes.medium)
                        )
                    }
                } else {
                    null
                },
                modifier = if (action == null) {
                    Modifier.clickable(onClick = onClick)
                } else {
                    Modifier.weight(1f)
                }
            )
            @Suppress("SENSELESS_COMPARISON")
            if (action != null) {
                Box(
                    modifier = Modifier
                        .align(CenterVertically)
                        .padding(end = MaterialTheme.spacing.small)
                ) {
                    action()
                }
            }
        }
        LinearProgressIndicator(
            progress = region.downloadProgress,
            modifier = Modifier
                .alpha(if (region.downloading) 1f else 0f)
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium)
        )
    }

    if (showCancelConfirmation) {
        ConfirmationDialog(
            title = stringResource(
                if (region.deleteRequested) {
                    R.string.cancel_delete_request_title
                } else {
                    R.string.cancel_download_confirmation_title
                }
            ),
            text = stringResource(
                if (region.deleteRequested) {
                    R.string.cancel_delete_request_text
                } else {
                    R.string.cancel_download_confirmation_text
                }
            ),
            cancelButtonText = stringResource(R.string.no),
            confirmButtonText = stringResource(R.string.yes),
            onDismissRequest = { showCancelConfirmation = false },
            onConfirm = {
                showCancelConfirmation = false
                onCancel(region)
            }
        )
    }
}

private inline fun getRegionAction(
    region: OfflineRegionResult.OfflineRegion,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    noinline onViewInLibrary: ((OfflineRegionResult.OfflineRegion) -> Unit)?,
    crossinline onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    crossinline onRetry: (OfflineRegionResult.OfflineRegion) -> Unit
): @Composable (() -> Unit)? =
    if (onViewInLibrary != null && (region.downloaded || region.deleteRequested)) {
        {
            TextButton(
                onClick = { onViewInLibrary(region) },
                modifier = Modifier.widthIn(max = 140.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.view_in_library).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    } else if (onViewInLibrary == null && (region.updateAvailable || region.downloadingUpdate)) {
        {}
    } else if (region.cancellable) {
        {
            TextButton(onClick = { onCancel(region) }, enabled = !cancellingDownload) {
                Text(
                    text = stringResource(id = R.string.cancel).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    } else if (region.downloadFailed) {
        {
            TextButton(onClick = { onRetry(region) }, enabled = !requestingDownload) {
                Text(
                    text = stringResource(id = R.string.retry).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    } else {
        null
    }

@Composable
private fun regionSecondaryText(region: OfflineRegionResult.OfflineRegion): String {
    val size = region.size ?: return ""
    val formattedSize = formatShortFileSize(LocalContext.current, size)
    return if (region.groupName.isNullOrBlank()) {
        formattedSize
    } else {
        "$formattedSize · ${region.groupName}"
    }
}

@Preview(showBackground = true)
@Composable
private fun OfflineRegionItemPreview(
    @PreviewParameter(OfflineRegionItemParamsProvider::class) params: OfflineRegionItemParams
) {
    AppTheme {
        OfflineRegionItem(
            region = params.region,
            cancellingDownload = false,
            requestingDownload = false,
            onClick = {},
            onCancel = {},
            onRetry = {},
            onViewInLibrary = {},
        )
    }
}

private class OfflineRegionItemParamsProvider : PreviewParameterProvider<OfflineRegionItemParams> {
    override val values: Sequence<OfflineRegionItemParams>
        get() {
            val dummyRegion = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }.regions.first()
            return sequenceOf(
                OfflineRegionItemParams(region = dummyRegion.copy(downloadOrder = null)),
                OfflineRegionItemParams(
                    region = dummyRegion.copy(
                        downloadOrder = DownloadOrder(
                            "",
                            null,
                            status = OfflineRegionStatus.REQUESTED,
                            downloadedSize = 0,
                            sourceTypeUsed = "TILE_LIST"
                        )
                    ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                    }
                ),
                OfflineRegionItemParams(
                    region = dummyRegion
                        .copy(
                            name = "A very long name that will not fit the space available on one line",
                            downloadOrder = DownloadOrder(
                                "",
                                null,
                                status = OfflineRegionStatus.UPDATE_IN_PROGRESS,
                                downloadedSize = 0,
                                sourceTypeUsed = "TILE_LIST"
                            )
                        ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                    }
                ),
                OfflineRegionItemParams(
                    region = dummyRegion
                        .copy(
                            name = "A very long name that will not fit the space available on one line or even on two lines and needs a third",
                            downloadOrder = DownloadOrder(
                                "",
                                null,
                                status = OfflineRegionStatus.FINISHED,
                                downloadedSize = 0,
                                sourceTypeUsed = "TILE_LIST"
                            )
                        ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.view_in_library))
                        }
                    }
                ),
                OfflineRegionItemParams(
                    region = dummyRegion
                        .copy(
                            downloadOrder = DownloadOrder(
                                "",
                                null,
                                status = OfflineRegionStatus.FAILED,
                                downloadedSize = 0,
                                sourceTypeUsed = "TILE_LIST"
                            )
                        ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.view_in_library))
                        }
                    }
                ),
            )
        }
}

private data class OfflineRegionItemParams(
    val region: OfflineRegionResult.OfflineRegion,
    val action: @Composable (() -> Unit)? = null
)
