package com.stt.android.offlinemaps.ui

import android.text.format.Formatter.formatShortFileSize
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.ListItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.util.highlightedString
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineRegionResult

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun OfflineRegionGroupItem(
    group: OfflineRegionResult.OfflineRegionGroup,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    highlight: String? = null,
) {
    ListItem(
        icon = {
            Box(modifier = Modifier.size(40.dp)) {
                Icon(
                    painter = painterResource(R.drawable.folder_outline),
                    contentDescription = null,
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.medium)
                        .align(Alignment.CenterStart),
                    tint = MaterialTheme.colors.secondary,
                )
            }
        },
        text = {
            Text(
                highlightedString(
                    text = group.name,
                    highlight = highlight,
                    highlightStyle = SpanStyle(
                        color = MaterialTheme.colors.primary,
                        fontWeight = FontWeight.Bold
                    )
                )
            )
        },
        secondaryText = {
            Text(groupSecondaryText(group))
        },
        trailing = {
            Icon(
                painter = painterResource(R.drawable.chevron_right),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.medium)
            )
        },
        modifier = modifier
            .clickable(onClick = onClick)
    )
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun groupSecondaryText(group: OfflineRegionResult.OfflineRegionGroup): String =
    buildString {
        append(
            pluralStringResource(
                R.plurals.offline_region_count,
                group.regions.size,
                group.regions.size
            )
        )
        append(" | ")
        // TODO: What format exactly should the size have? SI (base 1000) or IEC (1024)?
        // What should the precision be?
        append(formatShortFileSize(LocalContext.current, group.size))
    }

@Preview(showBackground = true)
@Composable
private fun OfflineRegionGroupItemPreview() {
    AppTheme {
        OfflineRegionGroupItem(
            group = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS[2],
            onClick = {}
        )
    }
}
