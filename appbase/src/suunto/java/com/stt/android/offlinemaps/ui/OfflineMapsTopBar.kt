package com.stt.android.offlinemaps.ui

import androidx.compose.foundation.layout.RowScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.compose.component.SuuntoTopBar

@Composable
fun OfflineMapsTopBar(
    titleText: String,
    onUpPressed: () -> Unit,
    modifier: Modifier = Modifier,
    actions: @Composable RowScope.() -> Unit = {},
) = SuuntoTopBar(
    title = titleText,
    onNavigationClick = onUpPressed,
    modifier = modifier,
    actions = actions,
)
