package com.stt.android.offlinemaps.watchstatus.entities

import com.stt.android.R

enum class WatchDownloadStatus {
    UNKNOWN,
    NOT_CONNECTED_TO_APP,
    BUSY,
    WIRELESS_NETWORK_DISABLED,
    WIFI_SETUP_MISSING,
    NOT_CHARGING,
    READY
}

val WatchDownloadStatus.icon
    get() = when (this) {
        WatchDownloadStatus.NOT_CONNECTED_TO_APP -> R.drawable.dl_status_not_connected
        WatchDownloadStatus.BUSY -> R.drawable.dl_status_busy
        WatchDownloadStatus.WIRELESS_NETWORK_DISABLED -> R.drawable.dl_status_wifi_disabled
        WatchDownloadStatus.WIFI_SETUP_MISSING -> R.drawable.dl_status_no_networks
        WatchDownloadStatus.NOT_CHARGING -> R.drawable.dl_status_not_charging
        WatchDownloadStatus.READY -> R.drawable.dl_status_all_set
        WatchDownloadStatus.UNKNOWN -> 0
    }

val WatchDownloadStatus.title
    get() = when (this) {
        WatchDownloadStatus.NOT_CONNECTED_TO_APP -> R.string.watch_dl_status_title_not_connected
        WatchDownloadStatus.BUSY -> R.string.watch_dl_status_title_busy
        WatchDownloadStatus.WIRELESS_NETWORK_DISABLED -> R.string.watch_dl_status_title_wifi_disabled
        WatchDownloadStatus.WIFI_SETUP_MISSING -> R.string.watch_dl_status_title_no_networks
        WatchDownloadStatus.NOT_CHARGING -> R.string.watch_dl_status_title_not_charging
        WatchDownloadStatus.READY -> R.string.watch_dl_status_title_ready
        WatchDownloadStatus.UNKNOWN -> 0
    }

val WatchDownloadStatus.subtitle
    get() = when (this) {
        WatchDownloadStatus.NOT_CONNECTED_TO_APP,
        WatchDownloadStatus.BUSY -> R.string.watch_dl_status_desc_downloads_can_be_queued_desc
        WatchDownloadStatus.WIRELESS_NETWORK_DISABLED,
        WatchDownloadStatus.WIFI_SETUP_MISSING,
        WatchDownloadStatus.NOT_CHARGING -> R.string.watch_dl_status_desc_required_to_download_desc
        WatchDownloadStatus.READY -> R.string.watch_dl_status_desc_downloaded_to_watch_desc
        WatchDownloadStatus.UNKNOWN -> 0
    }
