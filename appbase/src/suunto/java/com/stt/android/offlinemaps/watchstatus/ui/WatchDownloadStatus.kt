package com.stt.android.offlinemaps.watchstatus.ui

import android.content.Context
import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.offlinemaps.watchstatus.WatchDownloadStatusViewModel
import com.stt.android.offlinemaps.watchstatus.entities.WatchDownloadStatus
import com.stt.android.offlinemaps.watchstatus.entities.icon
import com.stt.android.offlinemaps.watchstatus.entities.subtitle
import com.stt.android.offlinemaps.watchstatus.entities.title
import com.stt.android.watch.wifi.WifiNetworksActivity

@Composable
fun WatchDownloadStatus(
    modifier: Modifier = Modifier,
    viewModel: WatchDownloadStatusViewModel = hiltViewModel(LocalActivity.current as ComponentActivity),
) {
    WatchDownloadStatusUi(
        viewModel.status,
        modifier = modifier.fillMaxWidth()
    )
}

@Composable
private fun WatchDownloadStatusUi(
    status: WatchDownloadStatus,
    modifier: Modifier = Modifier,
) {
    val onClick: (() -> Unit)? = getClickHandler(status, LocalContext.current)
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.spacing.medium)
            .height(80.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (status == WatchDownloadStatus.UNKNOWN) {
            Box(modifier = Modifier.fillMaxSize()) {
                CircularProgressIndicator(
                    Modifier.align(Center)
                )
            }
        } else {
            Image(
                painter = painterResource(id = status.icon),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.xlarge)
            )
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = MaterialTheme.spacing.medium)
            ) {
                Text(
                    text = stringResource(id = status.title),
                    style = MaterialTheme.typography.header
                )
                Text(
                    text = stringResource(id = status.subtitle),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colors.primaryVariant
                )
            }
            if (onClick != null) {
                TextButton(onClick = onClick) {
                    Text(
                        text = stringResource(
                            id = if (status == WatchDownloadStatus.WIRELESS_NETWORK_DISABLED) {
                                R.string.enable
                            } else {
                                R.string.add
                            }
                        )
                    )
                }
            }
        }
    }
}

private fun getClickHandler(
    status: WatchDownloadStatus,
    context: Context
): (() -> Unit)? = when (status) {
    WatchDownloadStatus.WIRELESS_NETWORK_DISABLED -> {
        {
            context.startActivity(
                WifiNetworksActivity.newStartIntent(context)
            )
        }
    }

    WatchDownloadStatus.WIFI_SETUP_MISSING -> {
        {
            context.startActivity(
                WifiNetworksActivity.newStartIntentToAddNetwork(context)
            )
        }
    }

    else -> null
}

@Preview(showBackground = true)
@Composable
private fun WatchStatusPreview(
    @PreviewParameter(WatchStatusParamsProvider::class) params: WatchStatusParams
) {
    AppTheme {
        WatchDownloadStatusUi(
            status = params.status,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

private class WatchStatusParamsProvider : PreviewParameterProvider<WatchStatusParams> {
    override val values: Sequence<WatchStatusParams> =
        WatchDownloadStatus.entries.map(::WatchStatusParams).asSequence()
}

private data class WatchStatusParams(
    val status: WatchDownloadStatus,
)
