package com.stt.android.watch

import android.content.Context
import android.content.Intent
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.flow.Flow

interface SuuntoPlusNavigator {
    fun isSuuntoPlusGuideSyncSupported(deviceType: SuuntoDeviceType?): Flow<Boolean>
    fun isStructuredWorkoutPlannerSupported(deviceType: SuuntoDeviceType?): Flow<Boolean>
    fun isSuuntoPlusFeatureSelectionSupported(): Flow<Boolean>
    fun isSuuntoPlusStoreSupported(deviceType: SuuntoDeviceType?): Boolean
    fun isSuuntoPlusWatchfaceSupported(): Flow<Boolean>
    fun newSuuntoPlusGuideIntent(context: Context): Intent
    fun newSuuntoPlusFeaturesIntent(context: Context): Intent
    fun newSuuntoPlusWatchfaceIntent(context: Context): Intent
    fun newSuuntoWatchfaceForDiLuIntent(context: Context): Intent
    fun newMyWatchFaceListIntent(context: Context): Intent
}
