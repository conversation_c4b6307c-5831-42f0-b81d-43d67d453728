package com.stt.android.watch

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.R
import com.stt.android.compose.component.SuuntoCard
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.primaryRed
import com.stt.android.compose.theme.spacing

enum class DeviceActionHeaderItem {
    GUIDE,
    WATCHFACE
}

/**
 * Top action items for device screen - displays horizontal list of action cards + vertical item list
 * Only used in DeviceConnectedFragment
 */
@Composable
fun DeviceActionHeaderScreen(
    onGuideClick: () -> Unit,
    onWatchFaceClick: () -> Unit = {},
    onMyGuideClick: () -> Unit,
    onMyWatchFaceClick: () -> Unit,
    modifier: Modifier = Modifier,
    supportsGuide: Boolean = true,
    supportsOnlineWatchFace: Boolean = true,
) {
    val actionItems = buildList {
        if (supportsGuide) {
            add(DeviceActionHeaderItem.GUIDE)
        }
        if (supportsOnlineWatchFace) {
            add(DeviceActionHeaderItem.WATCHFACE)
        }
    }

    if (actionItems.isNotEmpty()) {
        DeviceActionHeaderContent(
            actionItems = actionItems,
            onGuideClick = onGuideClick,
            onWatchFaceClick = onWatchFaceClick,
            modifier = modifier.fillMaxWidth()
        )
    }
}

/**
 * Horizontal action cards - extracted from original DeviceActionTopItems
 */
@Composable
private fun GuideAndWatchFaceCards(
    actionItems: List<DeviceActionHeaderItem>,
    modifier: Modifier = Modifier,
    onGuideClick: () -> Unit = {},
    onWatchFaceClick: () -> Unit = {},
) {
    BoxWithConstraints {
        LazyRow(
            modifier = modifier.fillMaxWidth(),
            horizontalArrangement = if (actionItems.size == 1) {
                Arrangement.Center
            } else {
                Arrangement.spacedBy(MaterialTheme.spacing.medium, Alignment.Start)
            },
            contentPadding = if (actionItems.size == 1) {
                PaddingValues(horizontal = MaterialTheme.spacing.small)
            } else {
                PaddingValues(horizontal = MaterialTheme.spacing.medium)
            }
        ) {
            items(actionItems) { item ->
                ActionItemContainer(maxWidth, actionItems.size) {
                    when (item) {
                        DeviceActionHeaderItem.GUIDE -> DeviceActionCard(
                            imageRes = R.drawable.suunto_plus_store_link_ad,
                            titleRes = R.string.suunto_plus_guides_title,
                            descriptionRes = R.string.suunto_plus_guides_device_action_subtitle,
                            onClick = onGuideClick
                        )

                        DeviceActionHeaderItem.WATCHFACE -> DeviceActionCard(
                            imageRes = R.drawable.suunto_plus_store_link_ad,
                            titleRes = R.string.watch_faces_features,
                            descriptionRes = R.string.suunto_plus_watch_faces_action_subtitle,
                            onClick = onWatchFaceClick
                        )
                    }
                }
            }
        }
    }
}

/**
 * Vertical action list that corresponds to horizontal cards
 * Shows the same actions as horizontal cards but in vertical list format
 * Uses Column instead of LazyColumn to avoid infinite height constraints in ComposeView
 */
@Composable
private fun DeviceActionHeaderContent(
    actionItems: List<DeviceActionHeaderItem>,
    modifier: Modifier = Modifier,
    onGuideClick: () -> Unit = {},
    onWatchFaceClick: () -> Unit = {},
) {
    Column(modifier = modifier.fillMaxWidth()) {
        GuideAndWatchFaceCards(
            actionItems = actionItems,
            onGuideClick = onGuideClick,
            onWatchFaceClick = onWatchFaceClick
        )

        actionItems.forEach { item ->
            when (item) {
                DeviceActionHeaderItem.GUIDE -> DeviceVerticalItem(
                    titleRes = R.string.suunto_plus_guides_title,
                    descriptionRes = R.string.suunto_plus_guides_device_action_subtitle,
                    onClick = onGuideClick
                )

                DeviceActionHeaderItem.WATCHFACE -> DeviceVerticalItem(
                    titleRes = R.string.watch_faces_features,
                    descriptionRes = R.string.suunto_plus_watch_faces_action_subtitle,
                    onClick = onWatchFaceClick
                )
            }
        }
    }
}

/**
 * Container for action items in LazyRow with responsive width calculation
 * Calculates width to show preview of second card
 */
@Composable
private fun ActionItemContainer(
    screenWidth: Dp,
    itemSize: Int,
    content: @Composable () -> Unit
) {
    val itemSpacing = MaterialTheme.spacing.medium
    val secondCardPreviewWidth = 24.dp

    val cardWidth = if (itemSize > 1) {
        val horizontalPadding = MaterialTheme.spacing.medium * 2
        screenWidth - horizontalPadding - itemSpacing - secondCardPreviewWidth
    } else {
        val horizontalPadding = MaterialTheme.spacing.small * 2
        screenWidth - horizontalPadding
    }

    Box(
        modifier = Modifier
            .width(cardWidth.coerceAtLeast(200.dp))
    ) {
        content()
    }
}

/**
 * Base action item card component using SuuntoCard
 */
@Composable
private fun DeviceActionCard(
    @DrawableRes imageRes: Int,
    @StringRes titleRes: Int,
    @StringRes descriptionRes: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    SuuntoCard(
        modifier = modifier
            .clickable { onClick() }
    ) {
        Box {
            Column {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(imageRes)
                        .crossfade(true)
                        .build(),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(104.dp),
                    contentScale = ContentScale.Crop,
                )

                // Action item content
                Column(
                    modifier = Modifier
                        .height(102.dp)
                        .padding(MaterialTheme.spacing.medium)
                ) {
                    Text(
                        text = stringResource(titleRes),
                        style = MaterialTheme.typography.bodyXLargeBold,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

                    Text(
                        text = stringResource(descriptionRes),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.secondary
                    )
                }
            }

            // NEW badge overlay
            NewBadge(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(MaterialTheme.spacing.small)
            )
        }
    }
}

/**
 * Vertical action item component - simpler layout for vertical list
 */
@Composable
private fun DeviceVerticalItem(
    @StringRes titleRes: Int,
    @StringRes descriptionRes: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium)
            .clickable { onClick() }
    ) {
        Text(
            text = stringResource(titleRes),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xxsmall))

        Text(
            text = stringResource(descriptionRes),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colorScheme.secondary
        )
    }
}

@Composable
private fun NewBadge(
    modifier: Modifier = Modifier
) {
    SuuntoCard(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp)
    ) {
        Text(
            text = stringResource(R.string.badge_new),
            modifier = Modifier
                .background(MaterialTheme.colorScheme.primaryRed)
                .padding(
                    horizontal = MaterialTheme.spacing.small,
                    vertical = MaterialTheme.spacing.xsmall
                ),
            color = MaterialTheme.colorScheme.onPrimary,
            style = MaterialTheme.typography.bodySmallBold,
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun DeviceActionHeaderPreview() {
    DeviceActionHeaderScreen(
        supportsGuide = true,
        supportsOnlineWatchFace = true,
        onGuideClick = {},
        onWatchFaceClick = {},
    )
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun DeviceActionOneItemPreview() {
    DeviceActionHeaderScreen(
        supportsGuide = true,
        supportsOnlineWatchFace = false,
        onGuideClick = {},
        onWatchFaceClick = {},
    )
}
