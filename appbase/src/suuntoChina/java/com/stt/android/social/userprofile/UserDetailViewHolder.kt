package com.stt.android.social.userprofile

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.databinding.ItemUserProfileDetailBinding
import com.stt.android.follow.UserFollowStatus
import com.stt.android.utils.PermissionUtils
import pub.devrel.easypermissions.EasyPermissions

internal class UserDetailViewHolder(
    activity: BaseUserProfileActivity,
    private val userDetailPresenter: UserDetailPresenter,
    currentUserController: CurrentUserController,
    binding: ItemUserProfileDetailBinding,
    userSettingsController: UserSettingsController,
    loadUser: () -> Unit,
    onDescriptionClicked: () -> Unit,
    onRealNameClicked: () -> Unit
) : BaseUserDetailViewHolder(
    activity,
    userDetailPresenter,
    currentUserController,
    binding,
    userSettingsController,
    loadUser,
    onDescriptionClicked,
    onRealNameClicked
) {
    override fun showFollowActionSpinner(userFollowStatus: UserFollowStatus?) {
        // do nothing
    }

    override fun onChangeProfileImage(needRequestPermission: Boolean) {
        // we need to check for storage permissions in china app, otherwise 360 app store rejects us
        if (needRequestPermission && !EasyPermissions.hasPermissions(activity, *PermissionUtils.STORAGE_PERMISSIONS)) {
            activity.showRequestPermissionConfirmationDialog()
        } else {
            userDetailPresenter.informProfileImageClick()
        }
    }
}
