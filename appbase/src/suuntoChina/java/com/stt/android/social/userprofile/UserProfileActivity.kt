package com.stt.android.social.userprofile

import android.content.Context
import android.content.Intent
import com.stt.android.domain.user.User

class UserProfileActivity {
    companion object {
        @JvmStatic
        fun newStartIntent(
            context: Context,
            userName: String,
            fromNotification: Boolean
        ): Intent {
            return BaseUserProfileActivity.newStartIntent(context, userName, fromNotification)
        }

        @JvmStatic
        fun newStartIntent(context: Context, user: User): Intent {
            return BaseUserProfileActivity.newStartIntent(context, user)
        }
    }
}
