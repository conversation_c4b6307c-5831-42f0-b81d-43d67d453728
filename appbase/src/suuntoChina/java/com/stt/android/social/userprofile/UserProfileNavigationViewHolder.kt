package com.stt.android.social.userprofile

import androidx.core.view.isVisible
import com.stt.android.FeatureFlags
import com.stt.android.controllers.CurrentUserController
import com.stt.android.databinding.ItemUserProfileNavigationBinding

class UserProfileNavigationViewHolder(
    listener: <PERSON><PERSON><PERSON>List<PERSON>,
    currentUserController: CurrentUserController,
    binding: ItemUserProfileNavigationBinding,
    featureFlags: FeatureFlags,
) : SuuntoUserProfileNavigationViewHolder(
    listener,
    currentUserController,
    binding,
    featureFlags
) {

    override fun setupNavigationItems() {
        binding.contactCustomerService.isVisible = true
        binding.contactCustomerServiceDivider.isVisible = true
        binding.contactCustomerService.setOnClickListener {
            listener.onContactCustomerServiceClicked()
        }

        binding.repairService.isVisible = true
        binding.repairServiceDivider.isVisible = true
        binding.repairService.setOnClickListener {
            listener.onAfterSalesServiceClicked()
        }

        super.setupNavigationItems()
    }
}
